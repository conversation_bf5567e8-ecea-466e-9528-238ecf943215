"use client";

import Image from "next/image";
import { useRef, useEffect, useState } from "react";
import { ArrowRightIcon } from "../common/icons";

interface Testimonial {
	id: number;
	quote: string;
	name: string;
	title: string;
	company: string;
	avatar: string;
}

const testimonialData: Testimonial[] = [
	{
		id: 1,
		quote:
			"AfricaSkillz was instrumental in my career transition. I found my current role through the platform and their skills assessment helped me stand out to employers.",
		name: "<PERSON><PERSON>",
		title: "Software Developer",
		company: "TechPro, Lagos",
		avatar: "/testimonial1.jpg",
	},
	{
		id: 2,
		quote:
			"As a recruiter, AfricaSkillz has transformed how we find talent. The platform's matching algorithm connects us with qualified candidates who align with our company culture.",
		name: "<PERSON><PERSON>",
		title: "HR Director",
		company: "GlobalFinance, Ghana",
		avatar: "/testimonial2.jpg",
	},
	{
		id: 3,
		quote:
			"The multilingual support makes AfricaSkillz accessible to professionals across the continent. I've used their training resources to upskill my entire team.",
		name: "<PERSON><PERSON>",
		title: "Marketing Manager",
		company: "Innovate, SA",
		avatar: "/testimonial3.jpg",
	},
];

const TestimonialCard = ({ testimonial }: { testimonial: Testimonial }) => {
	return (
		<div
			className="bg-white rounded-[12px] w-[214px] md:min-w-[407px] md:max-w-[407px] h-fit transition-shadow duration-300 relative py-4 px-[14px] md:pt-[32px] md:pr-[26px] md:pb-[32px] md:pl-[26px]"
			style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}>
			{/* Purple background accent */}
			{/* <div className="absolute inset-0 bg-purple-100 rounded-[12px] opacity-30"></div> */}

			<Image
				src="/quote-light.png"
				alt="Quote"
				width={250}
				height={177}
				className="md:absolute hidden md:block top-0 right-0 z-0"
			/>
			<Image
				src="/quote-light.png"
				alt="Quote"
				width={130}
				height={93}
				className="absolute md:hidden top-0 right-0 z-0 rounded-tr-[6px]"
			/>

			<div className="relative z-10 h-full flex flex-col gap-[81px]">
				{/* Quote */}
				<p className="text-[#71717A] font-[Pretendard] font-regular md:font-normal text-[9px] leading-[12px] md:text-[18px] md:leading-[24px] tracking-[-0.4px]">
					&quot;{testimonial.quote}&quot;
				</p>

				{/* Profile */}
				<div className="flex items-center gap-2.5 mt-auto">
					<div className="relative">
						<Image
							src={testimonial.avatar || "/placeholder.svg"}
							alt={testimonial.name}
							width={52}
							height={52}
							className="rounded-full hidden md:block object-cover"
						/>
						<Image
							src={testimonial.avatar || "/placeholder.svg"}
							alt={testimonial.name}
							width={27}
							height={27}
							className="rounded-full md:hidden object-cover"
						/>
					</div>
					<div>
						<h4 className="text-[#3F3F46] font-[Pretendard] font-semibold text-[8px] leading-[14px] md:text-[16px] md:leading-[26px] tracking-[-0.4px] md:mb-1">
							{testimonial.name}
						</h4>
						<p className="text-[#71717A] text-[8px] leading-[14px] md:text-[14px] md:leading-[18px]">
							{testimonial.title}, {testimonial.company}
						</p>
					</div>
				</div>
			</div>
		</div>
	);
};

export default function SuccessStories() {
	const [currentIndex, setCurrentIndex] = useState(0);
	const [cardWidth, setCardWidth] = useState(0);
	const cardRef = useRef<HTMLDivElement>(null);
	const GAP = 20; // px, as in your gap-8 (8 * 4 = 32px)

	// Measure card width on mount and when window resizes
	useEffect(() => {
		function updateCardWidth() {
			if (cardRef.current) {
				setCardWidth(cardRef.current.offsetWidth + GAP);
			}
		}
		updateCardWidth();
		window.addEventListener("resize", updateCardWidth);
		return () => window.removeEventListener("resize", updateCardWidth);
	}, []);

	const nextSlide = () => {
		setCurrentIndex((prev) => (prev + 1) % testimonialData.length);
	};

	const prevSlide = () => {
		setCurrentIndex(
			(prev) => (prev - 1 + testimonialData.length) % testimonialData.length
		);
	};

	return (
		<section className="py-[48px] md:py-[120px] bg-gray-50">
			<div className="w-full max-w-7xl mx-auto px-4">
				{/* Section Header */}
				<div className="text-center mb-16">
					<h2 className="text-[#27272A] font-[Pretendard] font-semibold text-[24px] leading-[32px] md:text-[48px] md:leading-[56px] tracking-[-1.5px] text-center mb-6">
						Our success stories
					</h2>
					<p className="text-[#52525B] font-[Pretendard] md:font-normal font-regular text-base leading-[26px] md:text-[24px] md:leading-[32px] tracking-[-0.5px] text-center max-w-3xl mx-auto">
						Hear from professionals, employers, and educators who have found
						<br className="hidden md:block" />
						success through our platform.
					</p>
				</div>

				{/* Testimonials Container */}
				<div className="relative">
					{/* Testimonials Track */}
					<div className="overflow-hidden">
						<div
							className="flex gap-8 transition-transform duration-500 ease-in-out"
							style={{
								transform: `translateX(-${currentIndex * cardWidth}px)`,
								width: cardWidth
									? `${testimonialData.length * cardWidth}px`
									: "auto",
							}}>
							{testimonialData.map((testimonial, idx) => (
								<div
									key={testimonial.id}
									ref={idx === 0 ? cardRef : null} // Attach ref only to the first card
								>
									<TestimonialCard testimonial={testimonial} />
								</div>
							))}
						</div>
					</div>

					{/* Navigation Arrows */}
					<div className="flex md:justify-center gap-4 mt-[28px] md:mt-12">
						<button
							onClick={prevSlide}
							className="md:w-[51px] md:h-[51px] w-[42px] h-[42px] cursor-pointer rounded-[38.86px] border-[1.21px] border-[#52525B] p-[8.5px] bg-white flex items-center justify-center hover:bg-neutral-50 transition-colors duration-200"
							aria-label="Previous testimonial">
							<ArrowRightIcon className="w-[34px] h-[34px] text-neutral-600 rotate-180" />
						</button>
						<button
							onClick={nextSlide}
							className="md:w-[51px] md:h-[51px] w-[42px] h-[42px] cursor-pointer rounded-[38.86px] border-[1.21px] border-[#52525B] p-[8.5px] bg-white flex items-center justify-center hover:bg-neutral-50 transition-colors duration-200"
							aria-label="Next testimonial">
							<ArrowRightIcon className="w-[34px] h-[34px] text-neutral-600" />
						</button>
					</div>
				</div>
			</div>
		</section>
	);
}
