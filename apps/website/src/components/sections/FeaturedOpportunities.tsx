"use client";

import OpportunityCard from "@/components/ui/OpportunityCard";
import { Button } from "../ui";

// Sample data - replace with your actual data source
const opportunitiesData = [
	{
		id: "1",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
	},
	{
		id: "2",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
	},
	{
		id: "3",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
	},
	{
		id: "4",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
	},
];

export default function FeaturedOpportunities() {
	const handleBookmark = (id: string) => {
		console.log("Bookmark opportunity:", id);
		// Implement bookmark functionality
	};

	const handleShare = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	const handleViewDetails = (id: string) => {
		console.log("View details for opportunity:", id);
		// Implement navigation to opportunity details
	};

	const handleViewMore = () => {
		console.log("View more opportunities");
		// Implement navigation to opportunities page
	};

	return (
		<section className="md:px-[90px] md:py-[120px] px-4 py-12 bg-white">
			<div >
				{/* Section Header */}
				<div className="flex flex-col md:flex-row items-start justify-between mb-12 gap-8 md:gap-0">
					<div>
						<h2 className="text-neutral-800 font-semibold text-[24px] leading-[32px] md:text-[48px] md:leading-[56px] md:mb-6">
							Featured Opportunities
						</h2>
						<p className="text-neutral-600 font-400 md:text-[24px] md:leading-[32px] text-base leading-[22px] ">
							Discover top opportunities across the continent
						</p>
					</div>
					
					{/* View More Button */}
					<Button
						onClick={handleViewMore}
						variant="primary"
						size="md">
						View More
					</Button>
				</div>

				{/* Opportunities Grid */}
				<div className="grid grid-cols-1 md:grid-cols-2 gap-5">
					{opportunitiesData.map((opportunity) => (
						<OpportunityCard
							key={opportunity.id}
							{...opportunity}
							onBookmark={handleBookmark}
							onShare={handleShare}
							onViewDetails={handleViewDetails}
						/>
					))}
				</div>
			</div>
		</section>
	);
}
