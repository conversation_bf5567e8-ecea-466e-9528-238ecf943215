import Image from "next/image";
import {
	BookIcon,
	ClockIcon,
	LightningIcon,
	SuitcaseIcon,
	ProfileIcon,
} from "../common/icons";
import { Button } from "../ui";

export default function Fingertips() {
	return (
		<section className="bg-[#F4EBF8] w-full px-4 pt-8 pb-12 sm:px-[90px] sm:pt-[120px] sm:pb-[177px] grid grid-cols-1 sm:grid-cols-2 gap-8 sm:gap-[87px]">
			<div>
				<div className="text-left mb-6 sm:mb-8">
					<h2 className="text-neutral-800 font-semibold text-2xl sm:text-[48px] sm:leading-[56px] mb-4 sm:mb-6">
						Our powerful Dashboard <br className="hidden sm:block" /> at Your
						Fingertips
					</h2>
				</div>
				<div className="flex flex-col gap-2.5 mb-8 sm:mb-[56px]">
					<div className="flex flex-wrap gap-2">
						<div className="flex items-center gap-2 px-2 py-1 sm:px-2.5 sm:py-2 border border-[#DCC0E8] rounded-full w-fit">
							<SuitcaseIcon
								className="text-[#8F34B4] h-4 w-4 sm:h-5 sm:w-5"
								color="#8F34B4"
							/>
							<p className="text-neutral-600 font-normal text-xs sm:text-[18px] sm:leading-[24px]">
								Real-time Job Application Tracking
							</p>
						</div>
					</div>
					<div className="flex flex-wrap gap-2">
						<div className="flex items-center gap-2 px-2 py-1 sm:px-2.5 sm:py-2 border border-[#DCC0E8] rounded-full w-fit">
							<BookIcon
								className="text-[#8F34B4] h-4 w-4 sm:h-5 sm:w-5"
								color="#8F34B4"
							/>
							<p className="text-neutral-600 font-normal text-xs sm:text-[18px] sm:leading-[24px]">
								Resume Management
							</p>
						</div>
						<div className="flex items-center gap-2 px-2 py-1 sm:px-2.5 sm:py-2 border border-[#DCC0E8] rounded-full w-fit">
							<ClockIcon
								className="text-[#8F34B4] h-4 w-4 sm:h-5 sm:w-5"
								color="#8F34B4"
							/>
							<p className="text-neutral-600 font-normal text-xs sm:text-[18px] sm:leading-[24px]">
								Seamless Job Application
							</p>
						</div>
					</div>
					<div className="flex flex-wrap gap-2">
						<div className="flex items-center gap-2 px-2 py-1 sm:px-2.5 sm:py-2 border border-[#DCC0E8] rounded-full w-fit">
							<LightningIcon
								className="text-[#8F34B4] h-4 w-4 sm:h-5 sm:w-5"
								color="#8F34B4"
							/>
							<p className="text-neutral-600 font-normal text-xs sm:text-[18px] sm:leading-[24px]">
								Career Growth Tracking
							</p>
						</div>
						<div className="flex items-center gap-2 px-2 py-1 sm:px-2.5 sm:py-2 border border-[#DCC0E8] rounded-full w-fit">
							<ProfileIcon
								className="text-[#8F34B4] h-4 w-4 sm:h-5 sm:w-5"
								color="#8F34B4"
							/>
							<p className="text-neutral-600 font-normal text-xs sm:text-[18px] sm:leading-[24px]">
								Personalised Dashboard
							</p>
						</div>
					</div>
				</div>
				<Button size="lg" className="w-fit hidden md:block  sm:w-auto">
					Continue to Dashboard
				</Button>
				<Button size="sm" className="w-fit md:hidden md:w-full sm:w-auto">
					Continue to Dashboard
				</Button>
				<p className="text-neutral-600 hidden font-semibold text-xs sm:text-[18px] sm:leading-[24px] mt-8 sm:mt-[106px]">
					Manage your professional journey{" "}
					<span className="font-normal">
						with our intuitive dashboard, <br className="hidden sm:block" />{" "}
						designed for the modern African professional
					</span>
				</p>
			</div>
			<div className="w-full h-[308px] md:h-full relative sm:mt-0">
				<Image
					src="/dashboard-img.png"
					alt="dashboard"
					fill
					className="object-contain"
					priority
				/>
			</div>

			<p className="text-[#27272A] font-semibold text-base leading-[26px] md:hidden sm:text-[18px] sm:leading-[24px] sm:mt-[106px]">
					Manage your professional journey{" "}
					<span className="font-normal">
						with our intuitive dashboard, <br className="hidden sm:block" />{" "}
						designed for the modern African professional
					</span>
				</p>
		</section>
	);
}
