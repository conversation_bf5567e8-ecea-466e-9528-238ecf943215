"use client";

import { useEffect, useState, useRef } from "react";
import { motion, useInView } from "framer-motion";

// Counter animation hook
const useCounter = (
	end: number,
	duration: number = 2000,
	start: number = 0
) => {
	const [count, setCount] = useState(start);
	const [isAnimating, setIsAnimating] = useState(false);

	const startAnimation = () => {
		if (isAnimating) return;
		setIsAnimating(true);

		const startTime = Date.now();
		const animate = () => {
			const now = Date.now();
			const progress = Math.min((now - startTime) / duration, 1);

			// Easing function for smooth animation
			const easeOutQuart = 1 - Math.pow(1 - progress, 4);
			const currentCount = Math.floor(start + (end - start) * easeOutQuart);

			setCount(currentCount);

			if (progress < 1) {
				requestAnimationFrame(animate);
			} else {
				setIsAnimating(false);
			}
		};

		requestAnimationFrame(animate);
	};

	return { count, startAnimation };
};

// Animated Stat Card Component
const AnimatedStatCard = ({
	number,
	label,
	delay = 0,
}: {
	number: string;
	label: string;
	delay?: number;
}) => {
	const ref = useRef(null);
	const isInView = useInView(ref, { once: true, margin: "-100px" });
	const [hasAnimated, setHasAnimated] = useState(false);

	// Extract numeric value for counter animation
	const numericValue = parseInt(number.replace(/\D/g, ""));
	const suffix = number.replace(/\d/g, "");

	const { count, startAnimation } = useCounter(numericValue, 2000);

	useEffect(() => {
		if (isInView && !hasAnimated) {
			setTimeout(() => {
				startAnimation();
				setHasAnimated(true);
			}, delay);
		}
	}, [isInView, hasAnimated, startAnimation, delay]);

	return (
		<motion.div
			ref={ref}
			initial={{ opacity: 0, y: 50, scale: 0.9 }}
			animate={
				isInView
					? { opacity: 1, y: 0, scale: 1 }
					: { opacity: 0, y: 50, scale: 0.9 }
			}
			transition={{
				duration: 0.6,
				delay: delay / 1000,
				ease: [0.25, 0.46, 0.45, 0.94],
			}}
			className="rounded-[7px] md:rounded-[16px] max-w-[273px] p-2.5 md:p-6 flex flex-col justify-between bg-[#DCC0E833] h-[160px] md:h-[370px] hover:bg-[#DCC0E840] transition-colors duration-300">
			<motion.p
				className="text-[36px] leading-[44px] font-regular md:text-[88px] md:font-400 text-neutral-100"
				initial={{ scale: 0.8 }}
				animate={isInView ? { scale: 1 } : { scale: 0.8 }}
				transition={{ duration: 0.5, delay: (delay + 200) / 1000 }}>
				{hasAnimated ? `${count}${suffix}` : `0${suffix}`}
			</motion.p>
			<motion.p
				className="text-[8px] leading-[11px] md:text-[20px] md:leading-[22px] font-600 font-semibold text-neutral-100"
				initial={{ opacity: 0 }}
				animate={isInView ? { opacity: 1 } : { opacity: 0 }}
				transition={{ duration: 0.4, delay: (delay + 400) / 1000 }}>
				{label.split(" ").map((word, index) => (
					<span key={index} className="block">
						{word}
					</span>
				))}
			</motion.p>
		</motion.div>
	);
};

export default function StatsSection() {
	const ref = useRef(null);
	const isInView = useInView(ref, { once: true, margin: "-50px" });

	return (
		<section
			ref={ref}
			className="py-8 px-4 sm:px-4 md:py-[120px] md:px-[72px] bg-brand-500">
			<div className="flex flex-col gap-8 md:grid md:grid-cols-[35%_1fr] md:gap-[50px]">
				<motion.div
					className="flex flex-col gap-4 md:gap-6 text-left md:text-left"
					initial={{ opacity: 0, x: -50 }}
					animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
					transition={{ duration: 0.8, ease: "easeOut" }}>
					<motion.h2
						className="font-semibold text-[24px] leading-[32px] sm:text-2xl  md:text-[48px] md:leading-[56px] text-white"
						initial={{ opacity: 0, y: 30 }}
						animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
						transition={{ duration: 0.6, delay: 0.2 }}>
						Making an Impact Across Africa
					</motion.h2>
					<motion.p
						className="font-semibold text-sm leading-[26px] sm:text-base md:text-[18px] md:leading-[24px] text-white"
						initial={{ opacity: 0, y: 20 }}
						animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
						transition={{ duration: 0.6, delay: 0.4 }}>
						See how we are transforming the professional landscape
					</motion.p>
				</motion.div>

				<div className="grid grid-cols-3 gap-2">
					<div className="max-w-[273px]">
						<AnimatedStatCard
							number="500+"
							label="Companies Registered"
							delay={200}
						/>
					</div>
					<div className="max-w-[273px]">
						<AnimatedStatCard
							number="704+"
							label="Candidates Registered"
							delay={400}
						/>
					</div>
					<div className="max-w-[273px]">
						<AnimatedStatCard
							number="32+"
							label="Courses Available"
							delay={600}
						/>
					</div>
				</div>
			</div>
		</section>
	);
}
