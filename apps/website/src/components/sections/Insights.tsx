"use client";

import ArticleCard from "@/components/ui/ArticleCard";
import { Button } from "../ui";
import { ArrowAngleIcon } from "../common/icons";

// Sample articles data - replace with your actual data source
const articlesData = [
	{
		id: "1",
		title: "Top 10 In-demand Skills Across Africa in 2025",
		description:
			"Discover the most sought-after professional skills that are driving the African job market...",
		image: "/insights1.png",
		category: "Career Insights",
		publishedDate: "Posted 12th May",
	},
	{
		id: "2",
		title: "Why remote work is transforming African Employment",
		description:
			"The rise of remote work has opened new doors for professionals across the contin...",
		image: "/insights2.png",
		category: "Workplace Trends",
		publishedDate: "Posted 12th May",
	},
	{
		id: "3",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/insights3.png",
		category: "Career Development",
		publishedDate: "Posted 12th May",
	},
];

export default function Insights() {
	const handleReadMore = (id: string) => {
		console.log("Read article:", id);
		// Implement navigation to article details
	};

	const handleViewMore = () => {
		console.log("View more articles");
		// Implement navigation to articles page
	};

	return (
		<section className="py-12 px-4 md:py-[120px] md:px-[90px] bg-white">
			<div>
				{/* Section Header */}
				<div className="flex flex-col md:flex-row items-start justify-between mb-12 gap-8 md:gap-0">
					<div>
						<h2 className="text-[#27272A] font-semibold text-[24px] leading-[32px] md:text-[48px] md:leading-[56px] mb-3 md:mb-6">
							Latest Insights & Resources
						</h2>
						<p className="text-[#52525B] font-400 text-base leading-[26px] md:text-[24px] md:leading-[32px]">
							Stay updated with the latest career advice, industry trends, and{" "}
							<br className="hidden md:block" /> professional development resources
						</p>
					</div>

					{/* View More Button */}
					<Button
						onClick={handleViewMore}
						variant="outline"
						size="md"
						className="md:flex w-fit hidden"
						rightIcon={<ArrowAngleIcon />}>
						View More articles
					</Button>
					<Button
						onClick={handleViewMore}
						variant="outline"
						size="xs"
						className="md:hidden "
						rightIcon={<ArrowAngleIcon />}>
						View More articles
					</Button>
				</div>

				{/* Articles Grid */}
				<div className="flex flex-col md:flex-row items-start gap-5">
					{articlesData.map((article) => (
						<ArticleCard
							key={article.id}
							{...article}
							onReadMore={handleReadMore}
						/>
					))}
				</div>
			</div>
		</section>
	);
}
