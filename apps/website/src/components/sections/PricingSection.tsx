"use client";

import { useState } from "react";
import { But<PERSON> } from "../ui";
import { CheckNewIcon } from "../common/icons";
import Image from "next/image";

interface PricingPlan {
	id: string;
	name: string;
	price: number;
	annualPrice: number;
	description: string;
	features: string[];
	isPopular?: boolean;
	buttonVariant: "primary" | "outline";
}

const pricingPlans: PricingPlan[] = [
	{
		id: "recruiters",
		name: "Recruiters Plan",
		price: 49,
		annualPrice: 39,
		description: "Perfect for small businesses",
		features: [
			"5 job postings per month",
			"Basic candidate search",
			"30-day job listings",
			"Email support",
			"Basic analytics",
		],
		buttonVariant: "outline",
	},
	{
		id: "employers",
		name: "Employers Plan",
		price: 129,
		annualPrice: 103,
		description: "Ideal for growing companies",
		features: [
			"15 job postings per month",
			"Detailed analytics",
			"Advanced candidate search",
			"Featured job listings",
			"Priority email support",
			"Team collaboration tools",
		],
		isPopular: true,
		buttonVariant: "primary",
	},
	{
		id: "facilitators",
		name: "Facilitators Plan",
		price: 299,
		annualPrice: 239,
		description: "For large organizations",
		features: [
			"Unlimited job postings",
			"Premium candidate search",
			"Featured job listings",
			"Dedicated account manager",
			"API access",
			"Custom reporting",
			"Employer branding tools",
		],
		buttonVariant: "outline",
	},
];

const tabs = [
	{ id: "employers", label: "For Employers" },
	{ id: "recruiters", label: "For Recruiters" },
	{ id: "facilitators", label: "For Facilitators" },
];

export default function PricingSection() {
	const [activeTab, setActiveTab] = useState("employers");
	const [isAnnual, setIsAnnual] = useState(false);
	// Remove cardWidth, cardRef, useEffect, and GAP state/logic

	const handleGetStarted = (planId: string) => {
		console.log(`Get started with ${planId} plan`);
		// Add your logic here
	};

	return (
		<section className="py-12 px-4 md:py-[120px] md:px-[90px] bg-white">
			<div className="max-w-7xl mx-auto">
				{/* Header */}
				<div className="text-center mb-6 sm:mb-18">
					<h2 className="font-semibold text-2xl leading-[32px] sm:text-[48px] sm:leading-[56px] tracking-[-1.5px] text-neutral-700 mb-2.5 sm:mb-6">
						Flexible Plans for Every Need
					</h2>
					<p className="text-[#52525B] text-base md:text-[24px] md:leading-[32px] max-w-2xl mx-auto">
						Choose the plan that works for you, whether you&apos;re a job
						seeker, <br className="hidden sm:block" />
						employer, or educational institution.
					</p>
				</div>

				{/* Tab Navigation */}
				<div className="flex justify-center mb-6 sm:mb-12">
					<div className="bg-neutral-200 rounded-[4.3px] md:rounded-[12px] p-1 flex flex-wrap gap-2 sm:gap-0">
						{tabs.map((tab) => (
							<button
								key={tab.id}
								onClick={() => setActiveTab(tab.id)}
								className={`px-4 py-[2.5px] sm:px-[106px] sm:py-3 rounded-[2.8px] md:rounded-[8px] cursor-pointer text-[9px] leading-[16px] font-regular md:text-lg md:leading-6 font-normal transition-all duration-200 ${
									activeTab === tab.id
										? "bg-white text-neutral-700 shadow-sm"
										: "text-neutral-600 hover:text-neutral-700"
								}`}>
								{tab.label}
							</button>
						))}
					</div>
				</div>

				{/* Billing Toggle */}
				<div className="flex justify-center items-center gap-2 sm:gap-4 mb-6 sm:mb-12">
					<span
						className={`text-[12px] md:text-[20px] leading-[16px] md:leading-6 ${
							!isAnnual
								? "text-neutral-500 font-semibold"
								: "text-neutral-500 font-normal"
						}`}>
						Monthly
					</span>
					<button
						onClick={() => setIsAnnual(!isAnnual)}
						className={`relative w-8 h-4 sm:w-12 sm:h-6 rounded-full transition-colors cursor-pointer duration-200 ${
							isAnnual ? "bg-brand-500" : "bg-neutral-300"
						}`}>
						<div
							className={`absolute top-0.5 w-3 h-3 sm:w-5 sm:h-5 bg-white rounded-full transition-transform duration-200 ${
								isAnnual ? "translate-x-4 sm:translate-x-6" : "translate-x-0.5"
							}`}
						/>
					</button>
					<span
						className={`text-[12px] md:text-[20px] leading-[16px] md:leading-6 ${
							isAnnual
								? "text-neutral-500 font-semibold"
								: "text-neutral-500 font-normal"
						}`}>
						Annual{" "}
						<span className="text-green-600 font-medium">(Save 20%)</span>
					</span>
				</div>

				{/* Pricing Cards - Carousel on mobile, grid on desktop */}
				<div className="relative">
					<div className="flex flex-nowrap md:grid md:grid-cols-3 w-full overflow-x-auto md:overflow-x-visible gap-2.5 md:gap-[20px]  mx-auto pb-2">
						{pricingPlans.map((plan) => (
							<div
								key={plan.id}
								className={`bg-white w-[204px]  md:w-[100%] h-fit rounded-[16px] px-4 py-8 md:px-8 md:py-[64px] transition-all duration-300 relative mx-auto ${
									plan.isPopular
										? "border border-success-500 md:shadow-lg md:scale-105"
										: "border border-neutral-200 hover:shadow-lg"
								}`}
								style={{
									boxShadow: "0px 16px 32px -12px #0E121B1A",
								}}>
								{/* Plan Header */}
								<div className="mb-4 sm:mb-5">
									<div className="flex justify-between items-center mb-3 sm:mb-5">
										<span className="px-2 py-1 sm:px-3 sm:py-1 rounded-full bg-[#EBEFFF]">
											<h3 className="text-brand-500 md:font-normal text-[10px] leading-[14px] sm:text-[20px] sm:leading-[26px]">
												{plan.name}
											</h3>
										</span>

										{plan.isPopular && (
											<div>
												<Image
													src="/popularbadge.png"
													alt="Popular"
													width={60}
													height={18}
													className="sm:w-[90px] sm:h-[26px]"
												/>
											</div>
										)}
									</div>
									<div className="mb-3 sm:mb-5 text-left">
										<span className="font-normal text-2xl md:text-[48px] md:leading-[64px] text-neutral-900">
											${isAnnual ? plan.annualPrice : plan.price}
										</span>
									</div>
									<p className="text-neutral-700 text-[12px] whitespace-nowrap md:whitespace-normal leading-[16px] md:text-[20px] font-normal md:leading-[32px]">
										{plan.description}
									</p>
								</div>

								{/* Features List */}
								<div className="mb-8 sm:mb-16">
									{plan.features.map((feature, index) => (
										<div
											key={index}
											className="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-4">
											<CheckNewIcon className="w-[11px] h-[11px] md:w-5 md:h-5 text-brand-500 flex-shrink-0" />

											<span className="text-neutral-700 text-[10px] whitespace-nowrap leading-[13px] md:text-[20px] md:leading-[26px] font-normal">
												{feature}
											</span>
										</div>
									))}
								</div>

								{/* CTA Button */}
								<Button
									variant={plan.buttonVariant}
									size="lg"
									fullWidth
									onClick={() => handleGetStarted(plan.id)}
									className="w-full hidden md:block">
									Get Started
								</Button>
								<Button
									variant={plan.buttonVariant}
									size="xs"
									fullWidth
									onClick={() => handleGetStarted(plan.id)}
									className="w-full md:hidden">
									Get Started
								</Button>
							</div>
						))}
					</div>
					{/* Navigation arrows for mobile only - removed as per design */}
				</div>
			</div>
		</section>
	);
}
