"use client";

export default function FeaturedProfiles() {
	return (
		<section className="py-10 px-2 sm:py-[120px] sm:px-[72px] bg-[#EBEFFF]">
			<div className="container-custom">
				<div className="text-center mb-8 sm:mb-12">
					<h2 className="text-neutral-800 font-semibold text-2xl leading-[32px] sm:text-[48px] sm:leading-[56px] mb-4 sm:mb-6">
						Unlock Your Potential
					</h2>
					<p className="text-neutral-600 font-400 text-base leading-[26px] sm:text-[24px] sm:leading-[32px]">
						Discover how AfricaSkillz is empowering professionals and
						organizations <br className="hidden sm:block" /> across the
						continent with innovative solutions.
					</p>
				</div>

				<div className="flex gap-4 sm:gap-5 overflow-x-auto sm:overflow-visible h-[300px] sm:h-[547px] pb-2 sm:pb-0">
					{/* Job Seekers Card */}
					<div className="rounded-[20px] relative overflow-hidden min-w-[80vw] sm:min-w-0 sm:w-[33%] h-full">
						<div
							className="absolute top-0 left-0 w-full h-full z-10 overflow-hidden"
							style={{
								backgroundImage: "url(/potential1.png)",
								backgroundSize: "cover",
								backgroundPosition: "center",
								backgroundRepeat: "no-repeat",
							}}>
							{/* Gradient Overlay */}
							<div className="absolute inset-0 bg-gradient-to-t from-[#002E31] to-transparent opacity-50"></div>
						</div>

						<div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 z-20">
							<div className="flex flex-col gap-1 sm:gap-2">
								<p className="text-neutral-50 font-400 text-xl sm:text-[36px] sm:leading-[44px]">
									For Job Seekers
								</p>
								<p className="font-400 text-sm sm:text-[20px] sm:leading-[26px] text-neutral-300">
									Discover opportunities that match your{" "}
									<br className="hidden sm:block" /> skills and career goals
									across the <br className="hidden sm:block" /> continent.
								</p>
							</div>
						</div>
					</div>

					{/* Employers Card */}
					<div className="rounded-[20px] relative overflow-hidden min-w-[80vw] sm:min-w-0 sm:w-[33%] h-full">
						<div
							className="absolute top-0 left-0 w-full h-full z-10 overflow-hidden"
							style={{
								backgroundImage: "url(/potential2.png)",
								backgroundSize: "cover",
								backgroundPosition: "center",
								backgroundRepeat: "no-repeat",
							}}>
							{/* Gradient Overlay */}
							<div className="absolute inset-0 bg-gradient-to-t from-[#002B11] to-transparent opacity-50"></div>
						</div>

						<div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 z-20">
							<div className="flex flex-col gap-1 sm:gap-2">
								<p className="text-neutral-50 font-400 text-xl sm:text-[36px] sm:leading-[44px]">
									For Employers
								</p>
								<p className="font-400 text-sm sm:text-[20px] sm:leading-[26px] text-neutral-300">
									Find the perfect talent for your{" "}
									<br className="hidden sm:block" /> organization with our
									advanced <br className="hidden sm:block" /> matching system.
								</p>
							</div>
						</div>
					</div>

					{/* Educators Card */}
					<div className="rounded-[20px] relative overflow-hidden min-w-[80vw] sm:min-w-0 sm:w-[33%] h-full">
						<div
							className="absolute top-0 left-0 w-full h-full z-10 overflow-hidden"
							style={{
								backgroundImage: "url(/potential3.png)",
								backgroundSize: "cover",
								backgroundPosition: "center",
								backgroundRepeat: "no-repeat",
							}}>
							{/* Gradient Overlay */}
							<div className="absolute inset-0 bg-gradient-to-t from-[#2B003B] to-transparent opacity-50"></div>
						</div>

						<div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 z-20">
							<div className="flex flex-col gap-1 sm:gap-2">
								<p className="text-neutral-50 font-400 text-xl sm:text-[36px] sm:leading-[44px]">
									For Educators
								</p>
								<p className="font-400 text-sm sm:text-[20px] sm:leading-[26px] text-neutral-300">
									Connect with learners and provide{" "}
									<br className="hidden sm:block" /> valuable skills training to
									Africa&apos;s <br className="hidden sm:block" /> workforce.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}
