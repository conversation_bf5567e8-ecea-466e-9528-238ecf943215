"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { Eye, EyeOff, Mail } from "lucide-react";

const carouselImages = ["/login-img.png", "/login-img2.png", "/login-img3.png"];

export default function LoginPage() {
	const [showPassword, setShowPassword] = useState(false);
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [current, setCurrent] = useState(0);

	const handleLogin = (e: React.FormEvent) => {
		e.preventDefault();
		// Handle login logic here
	};

	const handleDotClick = (idx: number) => {
		setCurrent(idx);
	};

	return (
		<div className="h-screen flex flex-col bg-white overflow-hidden">
			{/* Top Nav */}
			<nav className="w-full flex items-center justify-between px-4 py-2  border-b border-[#F4F4F5] bg-white z-50 flex-shrink-0">
				<Link href="/" className="flex items-center">
					<Image
						src="/africa-skillz-logo.png"
						alt="Africaskillz Logo"
						width={140}
						height={40}
					/>
				</Link>
			</nav>
			<div className="flex-1 flex flex-col md:flex-row min-h-0 h-0 overflow-hidden">
				{/* Left: Login Form */}
				<div className="flex-1 flex flex-col justify-center px-6 sm:px-12 md:px-24 py-8 h-full overflow-auto">
					<div className=" w-full mx-auto">
						<h1 className="text-[48px] leading-[56px] font-semibold text-[#27272A] mb-2">
							Welcome back!
						</h1>
						<p className="text-[24px] leading-[32px] font-semibold text-[#A1A1AA] mb-8">
							Jump right back in and continue with your <br /> applications
						</p>
						<form onSubmit={handleLogin} className="space-y-6">
							<div>
								<label
									htmlFor="email"
									className="block text-sm font-medium text-[#52525B] mb-2">
									Email
								</label>
								<div className="relative">
									<span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]">
										<Mail className="w-5 h-5" />
									</span>
									<input
										id="email"
										type="email"
										autoComplete="email"
										required
										value={email}
										onChange={(e) => setEmail(e.target.value)}
										placeholder="Enter your mail"
										className="w-full pl-10 pr-4 py-3 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-[#18181B] text-base focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#A1A1AA] placeholder:opacity-100"
									/>
								</div>
							</div>
							<div>
								<label
									htmlFor="password"
									className="block text-sm font-medium text-[#52525B] mb-2">
									Password
								</label>
								<div className="relative">
									<input
										id="password"
										type={showPassword ? "text" : "password"}
										autoComplete="current-password"
										required
										value={password}
										onChange={(e) => setPassword(e.target.value)}
										placeholder="********"
										className="w-full px-4 py-3 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#A1A1AA] placeholder:opacity-100"
									/>
									<button
										type="button"
										className="absolute right-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]"
										onClick={() => setShowPassword((v) => !v)}
										tabIndex={-1}>
										{showPassword ? (
											<EyeOff className="w-5 h-5" />
										) : (
											<Eye className="w-5 h-5" />
										)}
									</button>
								</div>
								<div className="flex justify-end mt-2">
									<Link
										href="#"
										className="text-sm text-blue-600 underline hover:underline font-medium">
										Forgot Password?
									</Link>
								</div>
							</div>
							<button
								type="submit"
								className="w-full py-3 rounded-lg bg-[#2563EB] cursor-pointer text-white font-semibold text-lg hover:bg-[#1746a2] transition-colors">
								Login
							</button>
						</form>
						<div className="text-center text-sm text-[#71717A] mt-6">
							Dont have an account?{" "}
							<Link
								href="#"
								className="text-blue-600 underline font-medium hover:underline">
								SignUp
							</Link>
						</div>
						<div className="flex items-center my-8">
							<div className="flex-1 h-px bg-[#E4E4E7]" />
							<span className="mx-4 text-[#A1A1AA] text-sm">OR</span>
							<div className="flex-1 h-px bg-[#E4E4E7]" />
						</div>
						<div className="space-y-4">
							<button className="w-full flex items-center justify-center gap-3 py-3 rounded-lg border border-[#E4E4E7] bg-[#F7F7F7] text-[#18181B] font-medium text-base hover:bg-[#F4F4F5] transition-colors">
								<Image
									src="/google-logo.png"
									alt="Google"
									width={20}
									height={20}
								/>
								Continue with Google
							</button>
							<button className="w-full flex items-center justify-center gap-3 py-3 rounded-lg border border-[#E4E4E7] bg-[#F7F7F7] text-[#18181B] font-medium text-base hover:bg-[#F4F4F5] transition-colors">
								<Image
									src="/facebook.png"
									alt="Facebook"
									width={20}
									height={20}
								/>
								Continue with Facebook
							</button>
						</div>
					</div>
				</div>
				{/* Right: Carousel Image and Features */}
				<div className="hidden md:flex flex-1 items-stretch h-full min-h-0 relative overflow-hidden">
					{/* Carousel Image */}
					<div className="relative w-full h-full flex items-center justify-center min-h-0">
						<Image
							src={carouselImages[current]}
							alt="Login Hero"
							fill
							className="object-cover w-full h-full"
							priority
						/>
						{/* Gradient Overlay */}
						<div className="absolute inset-0 bg-gradient-to-t from-[#00AEA4] to-transparent opacity-90 pointer-events-none z-10" />
						{/* Feature Badges */}
						<div className="absolute bottom-12 left-1/2 -translate-x-1/2 flex flex-wrap gap-4 justify-center w-full px-8 z-20">
							<span className="bg-white rounded-lg px-4 py-2 shadow text-[#18181B] text-base font-medium flex items-center gap-2">
								<span className="w-3 h-3 rounded-full bg-[#FBBF24] inline-block" />
								Manage Job Applications
							</span>
							<span className="bg-white rounded-lg px-4 py-2 shadow text-[#18181B] text-base font-medium flex items-center gap-2">
								<span className="w-3 h-3 rounded-full bg-[#A78BFA] inline-block" />
								Manage Job Procurements
							</span>
							<span className="bg-white rounded-lg px-4 py-2 shadow text-[#18181B] text-base font-medium flex items-center gap-2">
								<span className="w-3 h-3 rounded-full bg-[#60A5FA] inline-block" />
								Secure scholarships
							</span>
							<span className="bg-white rounded-lg px-4 py-2 shadow text-[#18181B] text-base font-medium flex items-center gap-2">
								<span className="w-3 h-3 rounded-full bg-[#FBBF24] inline-block" />
								View Events
							</span>
							<span className="bg-white rounded-lg px-4 py-2 shadow text-[#18181B] text-base font-medium flex items-center gap-2">
								<span className="w-3 h-3 rounded-full bg-[#F87171] inline-block" />
								Corporate Notices
							</span>
						</div>
						{/* Dot Controls */}
						<div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-3 z-30">
							{carouselImages.map((_, idx) => (
								<button
									key={idx}
									onClick={() => handleDotClick(idx)}
									className={`w-4 h-4 rounded-full bg-[#D4D4D8] border-white transition-all duration-200 ${
										current === idx ? "scale-125 bg-white" : ""
									}`}
									aria-label={`Go to slide ${idx + 1}`}
								/>
							))}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
