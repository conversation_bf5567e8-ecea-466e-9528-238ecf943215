"use client";
import Header from "@/components/layout/Header";
import Image from "next/image";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { LabelInput } from "@/components/ui";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";
import { Calendar } from "lucide-react";
import { ZoomIcon } from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import { useRouter } from "next/navigation";

const slides = [
	{
		image: "/events-img1.png",
		gradient: "from-[#193654] via-[#193654] to-transparent",
		texts: [
			{
				bg: "from-[#9747FF] to-[#F1CBBB]",
				text: "CAREER FAIR EVENTS",
				ml: "ml-0",
			},
			{
				bg: "from-[#F1CBBB] to-[#9747FF]",
				text: "PROMOTING GROWTH",
				ml: "ml-[96px]",
			},
		],
	},
	{
		image: "/events-img1.png",
		gradient: "from-[#9747FF] via-[#9747FF] to-transparent",
		texts: [
			{
				bg: "from-[#9747FF] to-[#F1CBBB]",
				text: "INNOVATION SUMMIT",
				ml: "ml-0",
			},
			{
				bg: "from-[#F1CBBB] to-[#9747FF]",
				text: "FUTURE LEADERS",
				ml: "ml-[96px]",
			},
		],
	},
	{
		image: "/events-img1.png",
		gradient: "from-[#193654] via-[#193654] to-transparent",
		texts: [
			{
				bg: "from-[#193654] to-[#F1CBBB]",
				text: "NETWORKING NIGHT",
				ml: "ml-0",
			},
			{
				bg: "from-[#F1CBBB] to-[#193654]",
				text: "CONNECT & GROW",
				ml: "ml-[96px]",
			},
		],
	},
];

interface EventCard {
	id: string;
	title: string;
	organizer: string;
	date: string;
	time: string;
	price: string;
	image: string;
	isFree: boolean;
}

const eventData: EventCard[] = [
	{
		id: "1",
		title: "The Lagos Network: For Tech startups.",
		organizer: "TechAcademy Africa",
		date: "Thur, May 8th",
		time: "2pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
	},
	{
		id: "2",
		title: "Digital Marketing Masterclass",
		organizer: "Marketing Hub Africa",
		date: "Fri, May 9th",
		time: "10am",
		price: "$56",
		image: "/corporate2.png",
		isFree: false,
	},
	{
		id: "3",
		title: "AI & Machine Learning Workshop",
		organizer: "Data Science Nigeria",
		date: "Sat, May 10th",
		time: "9am",
		price: "$96",
		image: "/corporate3.png",
		isFree: false,
	},
	{
		id: "4",
		title: "Fintech Innovation Summit",
		organizer: "Lagos Business School",
		date: "Mon, May 12th",
		time: "1pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
	},
	{
		id: "5",
		title: "UX/UI Design Bootcamp",
		organizer: "Design Academy",
		date: "Wed, May 14th",
		time: "11am",
		price: "$120",
		image: "/corporate2.png",
		isFree: false,
	},
	{
		id: "6",
		title: "Digital Marketing Masterclass",
		organizer: "Marketing Hub Africa",
		date: "Fri, May 9th",
		time: "10am",
		price: "$56",
		image: "/corporate2.png",
		isFree: false,
	},
	{
		id: "7",
		title: "AI & Machine Learning Workshop",
		organizer: "Data Science Nigeria",
		date: "Sat, May 10th",
		time: "9am",
		price: "$96",
		image: "/corporate3.png",
		isFree: false,
	},
	{
		id: "8",
		title: "Fintech Innovation Summit",
		organizer: "Lagos Business School",
		date: "Mon, May 12th",
		time: "1pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
	},
	{
		id: "9",
		title: "UX/UI Design Bootcamp",
		organizer: "Design Academy",
		date: "Wed, May 14th",
		time: "11am",
		price: "$120",
		image: "/corporate2.png",
		isFree: false,
	},
];

const upcomingEvents = [
	{
		id: "1",
		date: "Apr 11",
		day: "Tuesday",
		time: "4:00 PM",
		title: "Resume Reviews: Catching the Hiring managers Eye",
		avatars: [
			"/trusted-img-1.jpg",
			"/trusted-img-2.jpg",
			"/trusted-img-3.jpg",
			// "/trusted-img-4.jpg",
		],
		hosts: "By Tomi Makinde, Joe Eleganza & Mbuntu Pepe",
		platform: "Zoom",
		banner: "/meeting-banner1.png",
	},
	// Duplicate for demo
	{
		id: "2",
		date: "May 23",
		day: "Friday",
		time: "4:00 PM",
		title: "Transform Cold Outreach to Engaged conversations",
		avatars: [
			"/trusted-img-1.jpg",
			"/trusted-img-2.jpg",
			// "/trusted-img-3.jpg",
			// "/trusted-img-4.jpg",
		],
		hosts: "By Tomi Makinde, Joe Eleganza & Mbuntu Pepe",
		platform: "Zoom",
		banner: "/meeting-banner2.png",
	},
	{
		id: "3",
		date: "Feb 9",
		day: "Monday",
		time: "4:00 PM",
		title: "Resume Reviews: Catching the Hiring managers Eye",
		avatars: [
			"/trusted-img-1.jpg",
			"/trusted-img-2.jpg",
			"/trusted-img-3.jpg",
			// "/trusted-img-4.jpg",
		],
		hosts: "By Tomi Makinde, Joe Eleganza & Mbuntu Pepe",
		platform: "Zoom",
		banner: "/meeting-banner1.png",
	},
];

const UpcomingEventsCard = ({
	date,
	day,
	time,
	title,
	avatars,
	hosts,
	platform,
	banner,
}: (typeof upcomingEvents)[0]) => (
	<div className="flex items-center gap-[14px] pb-4">
		{/* line */}
		<div className="relative flex flex-col mb-auto items-center w-3 h-[100%] mt-[10px]">
			<span className="w-3 h-3 absolute top-0 bg-[#D4D4D8] rounded-full z-10"></span>
			<div className="absolute w-0.5 h-[309px]  bg-[#E4E4E7]"></div>
		</div>

		<div className="flex flex-col items-start  gap-4 ">
			<span className="font-semibold text-[24px] leading-[32px] text-[#3F3F46]">
				{date} <span className="font-regular">{day}</span>
			</span>

			<div className="px-6 py-[22px]  rounded-2xl bg-white border-0.5 border-[#E4E4E7] flex ">
				<div className="flex flex-col gap-4 max-w-[521px]">
					<p className="font-regular text-[20px] leading-[26px] text-[#A1A1AA]">
						{time}
					</p>
					<p className="font-semibold text-[24px] leading-[32px] text-[#3F3F46]">
						{title}
					</p>

					<div className="flex gap-4 ">
						<div className="flex -space-x-2">
							{avatars.map((src, idx) => (
								<div
									key={idx}
									className="w-7 h-7 rounded-full  border-2 border-white">
									<Image
										src={src}
										alt="avatar image"
										width={25}
										height={25}
										className="rounded-full "
									/>
								</div>
							))}
						</div>

						<p className="font-regular text-[20px] leading-[26px] text-[#A1A1AA]">
							{hosts}
						</p>
					</div>

					<div className="flex gap-2">
						<ZoomIcon />
						<span className="font-regular text-[20px] leading-[26px] text-[#A1A1AA]">
							{platform}
						</span>
					</div>
				</div>

				<div className="w-[160px] h-[160px] relative rounded-[8px] m-auto">
					<Image
						src={banner}
						className="object-cover"
						fill
						alt="meeting banner"
					/>
				</div>
			</div>
		</div>
	</div>
);

const EventCard = ({
	event,
	handleEventClick,
}: {
	event: EventCard;
	handleEventClick: (id: string) => void;
}) => {
	return (
		<div
			className="bg-white rounded-2xl overflow-hidden min-w-[85vw] sm:min-w-[320px] cursor-pointer transition-shadow duration-300"
			style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}
			onClick={() => handleEventClick(event.id)}>
			{/* Event Image */}
			<div className="relative h-32 overflow-hidden hover:scale-110 transition-transform duration-300 ease-in-out">
				<Image
					src={event.image}
					alt={event.title}
					className="w-full h-full object-cover"
					width={320}
					height={128}
				/>
			</div>

			{/* Event Content */}
			<div className="py-4 sm:py-6 px-3 sm:px-4">
				{/* Title */}
				<h3 className="text-lg sm:text-[24px] sm:leading-[32px] font-semibold text-neutral-600 mb-1 sm:mb-2 line-clamp-2">
					{event.title}
				</h3>

				{/* Organizer */}
				<p className="text-neutral-500 text-sm sm:text-[18px] sm:leading-6 mb-2 sm:mb-5">
					{event.organizer}
				</p>

				{/* Date & Time */}
				<div className="flex items-center gap-2 mb-4 sm:mb-6">
					<Calendar className="w-4 h-4 text-neutral-500 " />
					<span className="text-neutral-600 text-sm sm:text-[18px] sm:leading-6">
						{event.date} • {event.time}
					</span>
				</div>

				{/* Price */}
				<div className="flex justify-end border-t border-neutral-100 pt-4 sm:pt-6">
					<span
						className={`text-success-500 font-bold text-sm sm:text-[18px] sm:leading-6`}>
						{event.price}
					</span>
				</div>
			</div>
		</div>
	);
};

const filters = {
	category: [
		"Business",
		"Technology",
		"Healthcare",
		"Agriculture",
		"Education",
		"Marketing",
		"Design",
		"Language",
	],
	price: ["Free", "Paid", "Subscription"],
	countryRegion: [
		"West Africa",
		"Southern Africa",
		"Northern Africa",
		"Indian Ocean",
		"Eastern Africa",
		"Central Africa",
	],
};

const buildFilterSections = (): FilterSection[] => {
	return [
		{
			type: "checkbox",
			title: "Category",
			options: filters.category.map((c) => ({ label: c, value: c })),
			selected: [""],
			onChange: () => {},
			placeholder: "Select Category",
		},
		{
			type: "checkbox",
			title: "Price",
			options: filters.price.map((j) => ({ label: j, value: j })),
			selected: [""],
			onChange: () => {},
			placeholder: "Select Price",
		},
		{
			type: "checkbox",
			title: "Country Region",
			options: filters.countryRegion.map((j) => ({ label: j, value: j })),
			selected: [""],
			onChange: () => {},
			placeholder: "Select Regions",
		},
	];
};

export default function EventsPage() {
	const [current, setCurrent] = useState(0);
	const [direction, setDirection] = useState(0); // -1 for left, 1 for right
	const router = useRouter();

	const handleDotClick = (idx: number) => {
		if (idx === current) return;
		setDirection(idx > current ? 1 : -1);
		setCurrent(idx);
	};

	const handleEventClick = (id: string) => {
		router.push(`/events/${id}`);
	};

	return (
		<main>
			<Header />

			<section className="py-[60px] px-[82px] flex flex-col items-center gap-8 bg-white">
				<div className="w-full h-[382px] relative flex items-start rounded-2xl overflow-hidden">
					<AnimatePresence initial={false} custom={direction}>
						<motion.div
							key={current}
							className="absolute inset-0 w-full h-full"
							custom={direction}
							initial={{ x: direction > 0 ? "100%" : "-100%", opacity: 0 }}
							animate={{ x: 0, opacity: 1 }}
							exit={{ x: direction > 0 ? "-100%" : "100%", opacity: 0 }}
							transition={{ type: "tween", duration: 0.6 }}>
							<div className="flex flex-col gap-[14px] absolute top-[55%] left-[50px] z-40 -translate-y-1/2">
								{slides[current].texts.map((t, i) => (
									<div
										key={i}
										className={`p-2 bg-gradient-to-r ${t.bg} w-fit ${t.ml}`}>
										<span className="text-[48px] leading-[56px] font-semibold text-white ">
											{t.text}
										</span>
									</div>
								))}
							</div>

							<div className="w-50% h-[1300px] -top-40 relative ">
								<Image
									src={slides[current].image}
									fill
									className="object-contain z-20 object-right top-10 rounded-2xl"
									alt="Big image"
									priority
								/>
							</div>
							<div
								className={`absolute w-[65%] z-30 inset-0 rounded-2xl pointer-events-none bg-gradient-to-r ${slides[current].gradient}`}
							/>
						</motion.div>
					</AnimatePresence>
					{/* Dot controls */}
					<div className="absolute bottom-5 left-1/2 -translate-x-1/2 flex gap-3 z-30">
						{slides.map((_, idx: number) => (
							<button
								key={idx}
								onClick={() => handleDotClick(idx)}
								className={`w-4 h-4 rounded-full  bg-[#D4D4D8] cursor-pointer border-white transition-all duration-200 ${
									current === idx ? " scale-130" : "bg-[#D4D4D8]"
								}`}
								aria-label={`Go to slide ${idx + 1}`}
							/>
						))}
					</div>
				</div>
			</section>

			<section className="px-[90px] py-[47px] flex flex-col gap-[24px] bg-[#F4F4F5]">
				<LabelInput
					inputType="dropdown"
					className="w-[144px] ml-auto"
					value={"Last 7 days"}
					data={[
						{
							value: "Last 7 days",
							label: "Last 7 days",
						},
						{
							value: "Last 14 days",
							label: "Last 14 days",
						},
					]}
				/>

				<div className="flex gap-5">
					<div className="h-full">
						<FilterSidebar sections={buildFilterSections()} />
					</div>

					<div className="grid grid-cols-3 gap-5 sm:gap-5">
						{eventData.map((event) => (
							<EventCard
								key={event.id}
								event={event}
								handleEventClick={handleEventClick}
							/>
						))}
					</div>
				</div>
			</section>

			<div className="text-center bg-white pt-[120px] pb-[72px]">
				<h3 className="font-semibold text-[48px] leading-[56px] text-[#3F3F46] mb-6">
					Community Events
				</h3>
				<p className="font-regular text-[24px] leading-[32px] text-[#52525B]">
					Register now to join our indepth growth sessions with the best Career{" "}
					<br /> experts
				</p>
			</div>

			<section className="flex flex-col justify-center items-center pt-[48px]  bg-[#F4F4F5] ">
				<div className="max-w-[846px] h-[805px] overflow-y-auto">
					{upcomingEvents.map((event, index) => (
						<UpcomingEventsCard {...event} key={index} />
					))}
				</div>
			</section>

			<div className="bg-white pt-[120px]">
				<Footer />
			</div>
		</main>
	);
}
