"use client";

import { CalendarIcon, SuitcaseIconNew } from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";
import { useState, use } from "react";
import { ArrowRightIcon } from "lucide-react";

interface CourseCard {
	id: number;
	title: string;
	organizer: string;
	date: string;
	time: string;
	price: string;
	image: string;
	isFree: boolean;
	courseLevel: string;
}

const CourseCard = ({ course }: { course: CourseCard }) => {
	return (
		<div
			className="bg-white rounded-2xl overflow-hidden min-w-[85vw] sm:min-w-[320px] transition-shadow duration-300"
			style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}>
			{/* Event Image */}
			<div className="relative h-32 overflow-hidden">
				<Image
					src={course.image}
					alt={course.title}
					className="w-full h-full object-cover"
					width={320}
					height={128}
				/>

				<div className="absolute bottom-[10px] left-[10px] sm:bottom-[16px] sm:left-[16px]">
					<span className="bg-[#F4EBF8] backdrop-blur-sm text-[#8F34B4] px-2 sm:px-3 py-0.5 sm:py-1 rounded-full text-xs sm:text-base leading-[18px] sm:leading-[26px] font-normal">
						{course.courseLevel}
					</span>
				</div>
			</div>

			{/* Event Content */}
			<div className="py-4 sm:py-6 px-3 sm:px-4">
				{/* Title */}
				<h3 className="text-lg sm:text-[24px] sm:leading-[32px] font-semibold text-neutral-600 mb-1 sm:mb-2 line-clamp-2">
					{course.title}
				</h3>

				{/* Organizer */}
				<p className="text-neutral-500 text-sm sm:text-[18px] sm:leading-6 mb-2 sm:mb-5">
					{course.organizer}
				</p>

				{/* Date & Time */}
				<div className="flex items-center gap-2 mb-4 sm:mb-6">
					<CalendarIcon className="w-4 h-4 text-neutral-500 " />
					<span className="text-neutral-600 text-sm sm:text-[18px] sm:leading-6">
						{course.date}
					</span>
				</div>

				{/* Price */}
				<div className="flex justify-start border-t border-neutral-100 pt-4 sm:pt-6">
					<span
						className={`text-success-500 font-bold text-sm sm:text-[18px] sm:leading-6`}>
						{course.price}
					</span>
				</div>
			</div>
		</div>
	);
};

// Universities data array
const universitiesData = [
	{
		id: 1,
		image: "/brand1.png",
		universityName: "Babcock University",
		courseNumber: 22,
		country: "🇳🇬 Nigeria",
		description:
			"At Babcock University, we believe great ideas can come from anywhere. We care deeply about creating products that are helpful, equitable, and designed for everyone.",
		website: "https://babcock.edu.ng",
		email: "<EMAIL>",
		phone: "+***********",
		address: "Ilishan-Remo, Ogun State, Nigeria",
		specializations: ["Technology", "Business", "Healthcare", "Education"],
		activeCourses: 22,
	},
	{
		id: 2,
		image: "/brand2.png",
		universityName: "University of Cape Town",
		courseNumber: 33,
		country: "🇿🇦 South Africa",
		description:
			"At the University of Cape Town, we believe great ideas can come from anywhere. We care deeply about creating products that are helpful, equitable, and designed for everyone.",
		website: "https://uct.ac.za",
		email: "<EMAIL>",
		phone: "+2709111",
		address: "CapeTown, South Africa",
		specializations: ["Technology", "Innovation", "Scalable Systems"],
		activeCourses: 33,
	},
	{
		id: 3,
		image: "/brand3.png",
		universityName: "University of Lagos",
		courseNumber: 10,
		country: "🇳🇬 Nigeria",
		description:
			"At the University of Lagos, we believe great ideas can come from anywhere. We care deeply about creating products that are helpful, equitable, and designed for everyone.",
		website: "https://unilag.edu.ng",
		email: "<EMAIL>",
		phone: "+2340000",
		address: "Lagos, Nigeria",
		specializations: ["Engineering", "Medicine", "Arts", "Sciences"],
		activeCourses: 10,
	},
];

// Sample course data for the university
const courseData = [
	{
		id: 1,
		title: "Advanced Data Science Class",
		organizer: "TechAcademy Africa",
		date: "12th May - 13 June",
		time: "10:00 AM",
		price: "Free",
		image: "/course-img1.png",
		isFree: true,
		courseLevel: "Postgraduate",
	},
	{
		id: 2,
		title: "Business Management Fundamentals",
		organizer: "African Business School",
		date: "1st Aug - 13 September",
		time: "2:00 PM",
		price: "$52",
		image: "/course-img2.png",
		isFree: false,
		courseLevel: "Postgraduate",
	},
	{
		id: 3,
		title: "Digital Marketing Masterclass",
		organizer: "Lagos Marketing Institute",
		date: "6th May - 7 June",
		time: "11:00 AM",
		price: "$35",
		image: "/course-img1.png",
		isFree: false,
		courseLevel: "Postgraduate",
	},
	{
		id: 4,
		title: "Introduction to Web Development",
		organizer: "TechAcademy Africa",
		date: "12th May - 13 June",
		time: "9:00 AM",
		price: "Free",
		image: "/course-img2.png",
		isFree: true,
		courseLevel: "Postgraduate",
	},
	{
		id: 5,
		title: "Business Management Fundamentals",
		organizer: "African Business School",
		date: "1st Aug - 13 September",
		time: "3:00 PM",
		price: "$52",
		image: "/course-img1.png",
		isFree: false,
		courseLevel: "Postgraduate",
	},
	{
		id: 6,
		title: "Digital Marketing Masterclass",
		organizer: "Lagos Marketing Institute",
		date: "6th May - 7 June",
		time: "1:00 PM",
		price: "$35",
		image: "/course-img2.png",
		isFree: false,
		courseLevel: "Postgraduate",
	},
];

type Props = {
	params: Promise<{
		id: string;
	}>;
};

export default function UniversityDetailsPage({ params }: Props) {
	const [activeTab, setActiveTab] = useState("Courses Offered");
	const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
	const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
	const [selectedPrices, setSelectedPrices] = useState<string[]>([]);

	const unwrappedParams = use(params);
	const university = universitiesData.find(
		(r) => r.id === parseInt(unwrappedParams.id)
	);

	if (!university) {
		notFound();
	}

	// const handleBookmark = (id: string) => {
	// 	console.log("Bookmark course:", id);
	// };

	// const handleShare = (id: string) => {
	// 	console.log("Share course:", id);
	// };

	// const handleViewDetails = (id: string) => {
	// 	console.log("View course details:", id);
	// };

	const buildFilterSections = (): FilterSection[] => [
		{
			type: "checkbox",
			title: "CATEGORY",
			options: [
				{ label: "Business", value: "Business" },
				{ label: "Technology", value: "Technology" },
				{ label: "Healthcare", value: "Healthcare" },
				{ label: "Agriculture", value: "Agriculture" },
				{ label: "Education", value: "Education" },
				{ label: "Marketing", value: "Marketing" },
				{ label: "Design", value: "Design" },
				{ label: "Language", value: "Language" },
			],
			selected: selectedCategories,
			onChange: setSelectedCategories,
		},
		{
			type: "checkbox",
			title: "LEVEL",
			options: [
				{ label: "Beginner", value: "Beginner" },
				{ label: "Intermediate", value: "Intermediate" },
				{ label: "Advanced", value: "Advanced" },
			],
			selected: selectedLevels,
			onChange: setSelectedLevels,
		},
		{
			type: "checkbox",
			title: "PRICE",
			options: [
				{ label: "Free", value: "Free" },
				{ label: "Paid", value: "Paid" },
				{ label: "Subscription", value: "Subscription" },
			],
			selected: selectedPrices,
			onChange: setSelectedPrices,
		},
	];

	return (
		<main>
			<Header />

			{/* University Profile Section */}
			<section className="bg-white border-[#FAFAFA] py-[60px] px-[90px]">
				<div className=" ">
					{/* Back Link */}
					<div className="mb-8">
						<Link
							href="/directory/universities"
							className="flex items-center gap-2 text-[#71717A] hover:text-[#52525B] transition-colors">
							<ArrowRightIcon className="w-6 h-6 rotate-180" />
							<span className="text-sm font-medium leading-[22px]">
								Back to Universities
							</span>
						</Link>
					</div>

					{/* Recruiter Info */}
					<div className="flex flex-col md:flex-row items-start md:items-center gap-4 pb-10 border-b-[1.5px] border-[#E4E4E7]">
						<div className="flex-shrink-0">
							<div className="w-[104px] h-[104px] rounded-full border-2 border-[#E4E4E7] relative overflow-hidden">
								<Image
									src={university.image}
									fill
									className="object-cover"
									alt={`${university.universityName} logo`}
								/>
							</div>
						</div>
						<div className="flex-1">
							<h1 className="text-2xl md:text-[48px] leading-[64px] font-regular text-[#18181B] mb-2">
								{university.universityName}
							</h1>
							<div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 text-gray-600">
								<div className="flex items-center gap-2">
									<SuitcaseIconNew color="#71717A" />
									<span className="font-regular text-[24px] leading-[32px] text-[#3F3F46]">
										Hiring for {university.courseNumber} Positions
									</span>
								</div>
								<div className="font-regular text-[24px] leading-[32px] text-[#3F3F46]">
									<span className="text-lg">{university.country}</span>
								</div>
							</div>
						</div>
					</div>

					{/* Tabs */}
					<div className="mt-8 bg-[#E4E4E7] rounded-[12px] w-full px-[9px] py-[8px] flex items-center">
						<button
							onClick={() => setActiveTab("Courses Offered")}
							className={`rounded-[8px] w-1/2 py-[12px] flex cursor-pointer justify-center items-center transition-all duration-200 ${
								activeTab === "Courses Offered"
									? "bg-[#FFFFFF]"
									: "bg-transparent"
							}`}>
							<span className="font-regular text-[18px] leading-[24px] text-[#3F3F46]">
								Courses Offered
							</span>
						</button>
						<button
							onClick={() => setActiveTab("About University")}
							className={`rounded-[8px] w-1/2 py-[12px] flex cursor-pointer justify-center items-center transition-all duration-200 ${
								activeTab === "About University"
									? "bg-[#FFFFFF]"
									: "bg-transparent"
							}`}>
							<span className="font-regular text-[18px] leading-[24px] text-[#3F3F46]">
								About University
							</span>
						</button>
					</div>
				</div>
			</section>

			{/* Main Content */}
			{activeTab === "Courses Offered" ? (
				<section className="bg-[#F4F4F5] min-h-screen">
					<div className=" px-[60px] py-[90px]">
						<div className="flex flex-col lg:flex-row gap-5">
							{/* Filter Sidebar */}
							<div className="">
								<FilterSidebar
									sections={buildFilterSections()}
									onApplyFilters={() => {}}
									onClearAll={() => {
										setSelectedCategories([]);
										setSelectedLevels([]);
										setSelectedPrices([]);
									}}
								/>
							</div>

							{/* Course Listings */}
							<div className="flex-1">
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[20px]">
									{courseData.map((course) => (
										<CourseCard key={course.id} course={course} />
									))}
								</div>
							</div>
						</div>
					</div>
				</section>
			) : (
				/* About University Tab */
				<section className="bg-[#F4F4F5] py-[60px] px-[103px]">
					<div className="max-w-[810px]">
						<h2 className="text-2xl leading-[32px] font-semibold text-[#525252] mb-4">
							About {university.universityName}
						</h2>
						<p className="text-lg leading-[26px] font-regular text-[#71717A] mb-8">
							{university.description}
						</p>

						<div className="space-y-6">
							<div>
								<label className="text-2xl leading-[32px] font-semibold text-[#525252] mb-4">
									Industry
								</label>
								<p className="text-lg leading-[26px] font-regular text-[#71717A] mb-8">
									{university.specializations.join(", ")}
								</p>
							</div>

							<div>
								<label className="text-2xl leading-[32px] font-semibold text-[#525252] mb-4">
									Web address
								</label>
								<p className="text-lg leading-[26px] font-regular text-[#71717A] mb-8">
									{university.website.replace(/(https?:\/\/)/, "www.")}
								</p>
							</div>

							<div>
								<label className="text-2xl leading-[32px] font-semibold text-[#525252] mb-4">
									Email address
								</label>
								<p className="text-lg leading-[26px] font-regular text-[#71717A] mb-8">
									{university.email}
								</p>
							</div>

							<div>
								<label className="text-2xl leading-[32px] font-semibold text-[#525252] mb-4">
									Size
								</label>
								<p className="text-lg leading-[26px] font-regular text-[#71717A]">
									10K - 15K employees
								</p>
							</div>
						</div>
					</div>
				</section>
			)}

			<Footer />
		</main>
	);
}
