import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
	title: "Africa Skillz - Career Procurement & Development Platform",
	description:
		"Discover job opportunities across Africa, connect with top recruiters, and advance your career with Africa Skillz - the leading platform for African talent and employers.",
	keywords:
		"jobs, careers, Africa, recruitment, talent, employment, skills development",
	authors: [{ name: "Africa Skillz" }],
	creator: "Africa Skillz",
	publisher: "Africa Skillz",
	openGraph: {
		title: "Africa Skillz - Career Procurement & Development Platform",
		description:
			"Discover job opportunities across Africa, connect with top recruiters, and advance your career.",
		url: "https://africaskillz.com",
		siteName: "Africa Skillz",
		locale: "en_US",
		type: "website",
	},
	twitter: {
		card: "summary_large_image",
		title: "Africa Skillz - Career Procurement & Development Platform",
		description:
			"Discover job opportunities across Africa, connect with top recruiters, and advance your career.",
	},
	robots: {
		index: true,
		follow: true,
		googleBot: {
			index: true,
			follow: true,
			"max-video-preview": -1,
			"max-image-preview": "large",
			"max-snippet": -1,
		},
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en" className="scroll-smooth max-w-[1440px] bg-white m-auto">
			<body className="antialiased">{children}</body>
		</html>
	);
}
