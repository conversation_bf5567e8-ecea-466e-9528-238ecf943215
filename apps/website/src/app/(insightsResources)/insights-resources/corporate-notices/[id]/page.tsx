"use client";

import {
	ArrowAngleIcon,
	ArrowRightIcon,
	CalendarCheckIcon,
	CalendarIcon,
	CaretIcon,
	DownloadIcon,
	FileIcon,
	LocationIcon,
	MailIcon,
	OfficeIcon,
	PhoneIcon,
	ShareIcon,
} from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import Button from "@/components/ui/Button";
import Link from "next/link";
import { useParams } from "next/navigation";

// Data for corporate notices
const corporateNotices = [
	{
		id: "1",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "2",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "3",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "4",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "5",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "6",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
];

interface CorporateNoticesCardProps {
	id: string;
	title: string;
	description: string;
	organization: string;
	postedDate: string;
	onViewDetails: (id: string) => void;
}

// Update CorporateNoticesCard to accept typed props
const CorporateNoticesCard = ({
	id,
	title,
	description,
	organization,
	postedDate,
	onViewDetails,
}: CorporateNoticesCardProps) => {
	return (
		<div className="border border-[#E6E6E6] rounded-[12px] px-4 py-6 flex flex-col gap-5 cursor-pointer hover:shadow-sm transition-shadow">
			<div>
				<h3 className="font-semibold text-[24px] leading-[32px] text-[#52525B]">
					{title}
				</h3>
				<p className="font-regular text-[14px] leading-[24px] text-[#71717A] mt-2">
					{description}
				</p>
			</div>

			<div className="h-[1.5px] bg-[#F4F4F5] w-full"></div>

			<div className="">
				<div className="flex items-center gap-2">
					<OfficeIcon />{" "}
					<span className="font-regular text-[14px] leading-[24px] text-[#52525B] ">
						{organization}
					</span>
				</div>
				<div className="flex items-center gap-2 mt-4">
					<CalendarIcon />{" "}
					<span className="font-regular text-[14px] leading-[24px] text-[#52525B] ">
						Posted {postedDate}
					</span>
				</div>
			</div>

			<Button
				onClick={() => onViewDetails(id)}
				variant="outline"
				className="w-fit"
				size="xs"
				rightIcon={<ArrowAngleIcon color="#335CFF" />}>
				View details
			</Button>
		</div>
	);
};

export default function CorporateNoticesPage() {
	const params = useParams();
	const { id } = params;

	const notice = corporateNotices.find((notice) => notice.id === id);

	console.log(notice);

	const handleReadMore = (id: string) => {
		console.log("Read article:", id);
		// Implement navigation to article details
	};

	const handleViewMore = () => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	if (!notice) {
		return (
			<div className="min-h-screen flex flex-col items-center justify-center">
				<h1 className="text-3xl font-bold mb-4">Course Not Found</h1>
				<p className="text-lg">
					No Corporate Notice found for ID:{" "}
					<span className="font-mono">{id}</span>
				</p>
			</div>
		);
	}

	return (
		<main className="">
			<Header />
			<section className="w-full h-full py-8 px-[90px] bg-[#FAFAFA]">
				<Link
					href="/insights-resources"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] mb-[32px] hover:text-brand-500 transition-all duration-300 ">
					<ArrowRightIcon
						className="rotate-180 w-6 h-6 hover:text-brand-500"
						color="#52525B"
					/>{" "}
					Back to Events
				</Link>

				<section className="w-full h-full mt-8">
					<div className="grid grid-cols-[68%_30%] gap-[42px] mt-12">
						<div className="flex flex-col gap-5">
							<div>
								<h2 className="text-[48px] leading-[64px] font-regular text-[#18181B] mt-4 mb-5">
									{notice.title}
								</h2>
							</div>

							{/* white cards section */}
							<div className="grid grid-cols-4 gap-y-6 p-5 mt-6 bg-white mb-5">
								{/* details cards */}
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<LocationIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Location
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											San Fransisco, CA
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<MailIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Mail
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											<EMAIL>
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<PhoneIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Phone number
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											+****************
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<CalendarCheckIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Date Posted
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											1 day ago
										</p>
									</div>
								</div>
							</div>

							<div>
								<p className="font-semibold text-[24px] leading-[32px] text-[#52525B] mb-[14px]">
									Summary
								</p>
								<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
									The African Development Bank has announced significant changes
									to its financing policy for small and medium-sized enterprises
									(SMEs) in the agricultural sector. These updates aim to
									address the unique challenges faced by agricultural businesses
									across Eastern and Southern Africa.
								</p>
							</div>

							<div>
								<h3 className="font-semibold text-[24px] leading-[32px] text-[#52525B] mb-[14px]">
									Key Policy Changes
								</h3>
								<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
									The new policy framework introduces several important changes:
									<ul className="ml-5 list-disc">
										<li>
											Reduced Collateral Requirements: Recognizing the
											challenges agricultural SMEs face in providing traditional
											collateral, the bank will now accept alternative forms of
											security, including crop insurance policies, warehouse
											receipts, and equipment.
										</li>
										<li>
											Longer Grace Periods: To accommodate agricultural
											production cycles, grace periods on loan repayments have
											been extended to up to 12 months for seasonal crops and 24
											months for perennial crops.
										</li>
										<li>
											Technical Assistance Package: Borrowers will receive
											complementary business development services, including
											financial management training, agricultural best
											practices, and market linkage support.
										</li>
										<li>
											Green Financing Incentives: Preferential interest rates
											will be offered to businesses implementing climate-smart
											agricultural practices or renewable energy solutions.
										</li>
									</ul>
								</p>
							</div>

							<div className=" flex flex-col gap-3.5">
								<p className="font-semibold text-[24px] leading-[32px] text-[#52525B] ">
									Attachments
								</p>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Policy Framework Document.pdf
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												1.2 MB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Application Guidelines.pdf
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												920 KB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>

						<aside>
							<div className="px-6 py-8 rounded-[16px] border-[0.75px] border-[#E4E4E7] bg-white flex flex-col gap-[14px] h-fit">
								<div className="">
									<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
										Company
									</p>
									<div className="flex items-center gap-2">
                                        <OfficeIcon/>
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											TechAcademy Africa
										</span>
									</div>
								</div>
							</div>

							<Button
								variant="primary"
								rightIcon={<CaretIcon color="white" />}
								className="mt-4 w-full">
								Visit official website
							</Button>
							<Button
								variant="outline"
								leftIcon={<ShareIcon color="#335CFF" />}
								className="mt-4 w-full">
								Share Notice
							</Button>
						</aside>
					</div>
				</section>
			</section>

			<section className="px-[90px] py-[120px] bg-white">
				<div>
					{/* Section Header */}
					<div className="flex items-start justify-between mb-12">
						<div>
							<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
								More in this series
							</h2>
							
						</div>

						{/* View More Button */}
						<Button
							onClick={handleViewMore}
							variant="primary"
							size="md"
							// rightIcon={<ArrowAngleIcon />}
                            >
							View all
						</Button>
					</div>

					{/* Articles Grid */}
					<div className="flex items-start gap-5">
						{corporateNotices.slice(0, 4).map((notice) => (
							<CorporateNoticesCard
								key={notice.id}
								{...notice}
								onViewDetails={handleReadMore}
							/>
						))}
					</div>
				</div>
			</section>

			<Footer />
		</main>
	);
}
