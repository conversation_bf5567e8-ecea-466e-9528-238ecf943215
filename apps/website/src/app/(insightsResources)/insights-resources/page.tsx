"use client";
import {
	ArrowAngleIcon,
	ArticleIcon,
	AudioIcon,
	CalendarIcon,
	FilterIcon,
	OfficeIcon,
	VideoIcon,
} from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import { Button, LabelInput, Tab } from "@/components/ui";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";

import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";

interface ArticleCardProps {
	id: string;
	title: string;
	description: string;
	image: string;
	category: string;
	publishedDate: string;
	isAudio?: boolean;
	isArticle?: boolean;
	onReadMore?: (id: string) => void;
}

function ArticleCardInsights({
	id,
	title,
	description,
	image,
	category,
	publishedDate,
	onReadMore,
	isArticle,
	isAudio,
}: ArticleCardProps) {
	const handleReadMore = () => {
		onReadMore?.(id);
	};

	return (
		<div
			className="bg-white rounded-[24px] border border-[#E4E4E7] overflow-hidden transition-shadow duration-300 cursor-pointer group px-3 py-3 sm:px-4 sm:py-4 w-full"
			style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}
			onClick={handleReadMore}>
			{/* Article Image with Category Badge */}
			<div className="relative h-[249px] sm:h-[249px] overflow-hidden rounded-[10px] mb-4 sm:mb-6">
				<Image
					src={image}
					alt={title}
					className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
					fill
					priority
				/>

				{}
				{/* Category Badge - positioned at bottom left with 10px margin */}
				<div className="absolute bottom-[10px] left-[10px] sm:bottom-[16px] sm:left-[16px]">
					<span className="bg-[#F4F4F5] backdrop-blur-sm px-3 sm:px-3 py-1 sm:py-1 rounded-[8px] flex items-center">
						{isArticle ? (
							<ArticleIcon />
						) : isAudio ? (
							<AudioIcon />
						) : (
							<VideoIcon />
						)}
					</span>
				</div>
			</div>

			{/* Article Content */}
			<div className="relative">
				{/* Category Badge - positioned at bottom left with 10px margin */}
				<div className="mb-6 ">
					<span className="bg-[#F4EBF8] backdrop-blur-sm text-[#8F34B4] px-2 sm:px-3 py-0.5 sm:py-1 rounded-full text-xs sm:text-base leading-[18px] sm:leading-[26px] font-normal">
						{category}
					</span>
				</div>
				{/* Title */}
				<h3 className="text-base sm:text-[20px] sm:leading-[32px] font-semibold text-neutral-600 mb-1.5 sm:mb-2.5 group-hover:text-[#335CFF] transition-colors duration-200">
					{title}
				</h3>

				{/* Description */}
				<p className="text-neutral-400 text-xs sm:text-[16px] sm:leading-[26px] mb-1.5 sm:mb-2.5 line-clamp-2">
					{description}
				</p>

				{/* Meta Information */}
				<div className="flex flex-col items-start justify-between gap-3 sm:gap-5">
					{/* Published Date */}
					<div className="flex items-center gap-2 text-sm sm:text-lg leading-[18px] sm:leading-[24px] text-neutral-600">
						<CalendarIcon className="w-4 h-4 sm:w-6 sm:h-6" />
						<span>{publishedDate}</span>
					</div>

					{/* Read More Button */}
					<Button
						variant="primary"
						size="sm"
						rightIcon={
							<ArrowAngleIcon color="#fff" className="w-4 h-4 sm:w-5 sm:h-5" />
						}
						onClick={(e) => {
							e.stopPropagation();
							handleReadMore();
						}}
						className="w-full sm:w-auto">
						Read article
					</Button>
				</div>
			</div>
		</div>
	);
}

const PillComponent = ({ tag }: { tag: string }) => {
	return (
		<span
			className={
				"text-neutral-500 border-neutral-200  bg-neutral-100 px-2 sm:px-5.5 py-0.5 sm:py-[7px] text-xs sm:text-[20px] leading-[18px] sm:leading-[26px] rounded-full font-regular"
			}>
			{tag}
		</span>
	);
};

const tags = [
	"All",
	"Technology",
	"Business",
	"Agriculture",
	"Healthcare",
	"Energy",
	"Education",
	"Arts & Culture",
	"Fashion",
];

const articlesData = [
	{
		id: "1",
		title: "Top 10 In-demand Skills Across Africa in 2025",
		description:
			"Discover the most sought-after professional skills that are driving the African job market...",
		image: "/insights1.png",
		category: "Career Insights",
		publishedDate: "Posted 12th May",
	},
	{
		id: "2",
		title: "Why remote work is transforming African Employment",
		description:
			"The rise of remote work has opened new doors for professionals across the contin...",
		image: "/trending-insights1.jpg",
		category: "Workplace Trends",
		publishedDate: "Posted 12th May",
		isAudio: true,
	},
	{
		id: "3",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
	{
		id: "4",
		title: "Top 10 In-demand Skills Across Africa in 2025",
		description:
			"Discover the most sought-after professional skills that are driving the African job market...",
		image: "/insights1.png",
		category: "Career Insights",
		publishedDate: "Posted 12th May",
	},
	{
		id: "5",
		title: "Why remote work is transforming African Employment",
		description:
			"The rise of remote work has opened new doors for professionals across the contin...",
		image: "/trending-insights1.jpg",
		category: "Workplace Trends",
		publishedDate: "Posted 12th May",
		isAudio: true,
	},
	{
		id: "6",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
];
const articlesTabData = [
	{
		id: "1",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
	{
		id: "2",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
	{
		id: "3",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
	{
		id: "4",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
	{
		id: "5",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
	{
		id: "6",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
];
const technologyData = [
	{
		id: "1",
		title: "Top 10 In-demand Skills Across Africa in 2025",
		description:
			"Discover the most sought-after professional skills that are driving the African job market...",
		image: "/insights1.png",
		category: "Career Insights",
		publishedDate: "Posted 12th May",
	},
	{
		id: "2",
		title: "Why remote work is transforming African Employment",
		description:
			"The rise of remote work has opened new doors for professionals across the contin...",
		image: "/trending-insights1.jpg",
		category: "Workplace Trends",
		publishedDate: "Posted 12th May",
		// isAudio: true,
	},
	{
		id: "3",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		// isArticle: true,
	},
];

const tabs = ["All", "Articles", "Videos", "Corporate Notices", "Podcasts"];

const filters = {
	category: [
		"Business",
		"Technology",
		"Healthcare",
		"Agriculture",
		"Education",
		"Marketing",
		"Design",
		"Language",
	],
	countryRegion: [
		"West Africa",
		"Southern Africa",
		"Northern Africa",
		"Indian Ocean",
		"Eastern Africa",
		"Central Africa",
	],
};

// Data for corporate notices
const corporateNotices = [
	{
		id: "1",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "2",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "3",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "4",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "5",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "6",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
];

// Define props type for the card
interface CorporateNoticesCardProps {
	title: string;
	description: string;
	organization: string;
	postedDate: string;
	onClick: () => void;
}

// Update CorporateNoticesCard to accept typed props
const CorporateNoticesCard = ({
	title,
	description,
	organization,
	postedDate,
	onClick,
}: CorporateNoticesCardProps) => {
	return (
		<div className="border border-[#E6E6E6] rounded-[12px] px-4 py-6 flex flex-col gap-5 cursor-pointer hover:shadow-sm transition-shadow">
			<div>
				<h3 className="font-semibold text-[24px] leading-[32px] text-[#52525B]">
					{title}
				</h3>
				<p className="font-regular text-[14px] leading-[24px] text-[#71717A] mt-2">
					{description}
				</p>
			</div>

			<div className="h-[1.5px] bg-[#F4F4F5] w-full"></div>

			<div className="">
				<div className="flex items-center gap-2">
					<OfficeIcon />{" "}
					<span className="font-regular text-[14px] leading-[24px] text-[#52525B] ">
						{organization}
					</span>
				</div>
				<div className="flex items-center gap-2 mt-4">
					<CalendarIcon />{" "}
					<span className="font-regular text-[14px] leading-[24px] text-[#52525B] ">
						Posted {postedDate}
					</span>
				</div>
			</div>

			<Button
				onClick={onClick}
				variant="primary"
				className="w-fit"
				size="xs"
				rightIcon={<ArrowAngleIcon color="white" />}>
				View details
			</Button>
		</div>
	);
};

export default function InsightsResourcesPage() {
	const router = useRouter();
	const pathname = usePathname();

	const [activeTab, setActiveTab] = useState("All"); // Capitalized to match tab names
	const [insightsFilter] = useState("Last 7 days");
	const handleTabChange = (tab: string) => {
		// Add type annotation
		setActiveTab(tab);
		console.log(tab);
	};

	const handleViewMore = () => {
		console.log("View more articles");
		// Implement navigation to articles page
	};
	const handleReadMore = (id: string) => {
		router.push(`${pathname}/${id}`);

		console.log("View more articles");
		// Implement navigation to articles page
	};

	const buildFilterSections = (): FilterSection[] => {
		return [
			{
				type: "checkbox",
				title: "Category",
				options: filters.category.map((c) => ({ label: c, value: c })),
				selected: [""],
				onChange: () => {},
				placeholder: "Select Category",
			},
			{
				type: "checkbox",
				title: "Country Region",
				options: filters.countryRegion.map((j) => ({ label: j, value: j })),
				selected: [""],
				onChange: () => {},
				placeholder: "Select Regions",
			},
		];
	};

	return (
		<main className="w-full h-full">
			<Header />

			<section className="py-[60px] px-[82px] flex flex-col items-center gap-8 bg-[#FAFAFA]">
				<div className="text-center flex flex-col items-center gap-10">
					<h2 className="text-[72px] leading-[56px] font-medium text-[#27272A]">
						Insights & Resources
					</h2>
					<p className="font-semibold text-[20px] leading-[26px] text-[#A1A1AA] max-w-[744px]">
						Stay informed with the latest trends, events, and opportunities
						across Africa&apos;s professional landscape.
					</p>
				</div>

				<div className="w-full h-[382px] relative rounded-2xl">
					<Image
						src="/insights-img.png"
						fill
						className="object-cover rounded-2xl"
						alt="Big image"
					/>
				</div>

				<div className="flex gap-4 flex-wrap justify-center max-w-[823px] ">
					{tags.map((tag, index) => (
						<PillComponent tag={tag} key={index} />
					))}
				</div>
			</section>

			<section className="px-[89px] py-[72px] flex flex-col gap-[72px] bg-white">
				<div className="flex items-center justify-between">
					<Tab
						tabs={tabs}
						activeTab={activeTab}
						onTabChange={handleTabChange}
						className="border-none bg-[#FAFAFA] rounded-2xl w-fit "
					/>

					{activeTab.toLowerCase() === "corporate notices" && (
						<LabelInput
							inputType="dropdown"
							value={insightsFilter}
							dropdownLeftIcon={<FilterIcon />}
							data={[
								{ label: "Last 7 days", value: "Last 7 days" },
								{ label: "Last 14 days", value: "Last 14 days" },
							]}
							// onChange={(value)=> setInsightsFilter(value)}
						/>
					)}
				</div>

				{activeTab === "All" ? (
					<div className="flex flex-col gap-[72px]">
						<div>
							{/* Section Header */}
							<div className="flex items-start justify-between mb-12">
								<div>
									<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
										Trending Insights
									</h2>
									<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
										Stay updated with the latest career advice, industry trends,
										and <br />
										professional development resources
									</p>
								</div>

								{/* View More Button */}
								<Button
									onClick={handleViewMore}
									variant="outline"
									size="md"
									rightIcon={<ArrowAngleIcon />}>
									View More articles
								</Button>
							</div>

							{/* Articles Grid */}
							<div className="grid grid-cols-3  gap-5">
								{articlesData.map((article) => (
									<ArticleCardInsights
										key={article.id}
										{...article}
										onReadMore={handleReadMore}
									/>
								))}
							</div>
						</div>

						<div className="w-full border-t-[1.5px] border-[#E4E4E7]"></div>

						<div>
							{/* Section Header */}
							<div className="flex items-start justify-between mb-12">
								<div>
									<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
										Technology
									</h2>
									<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
										Stay updated with the latest career advice, industry trends,
										and <br /> professional development resources
									</p>
								</div>

								{/* View More Button */}
								<Button
									onClick={handleViewMore}
									variant="outline"
									size="md"
									rightIcon={<ArrowAngleIcon />}>
									View more articles
								</Button>
							</div>

							{/* Articles Grid */}
							<div className="grid grid-cols-3  gap-5">
								{technologyData.map((article) => (
									<ArticleCardInsights
										key={article.id}
										{...article}
										onReadMore={() => handleReadMore(article.id)}
									/>
								))}
							</div>
						</div>
						<div className="w-full border-t-[1.5px] border-[#E4E4E7]"></div>

						<div>
							{/* Section Header */}
							<div className="flex items-start justify-between mb-12">
								<div>
									<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
										Business
									</h2>
									<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
										Stay updated with the latest career advice, industry trends,
										and <br /> professional development resources
									</p>
								</div>

								{/* View More Button */}
								<Button
									onClick={handleViewMore}
									variant="outline"
									size="md"
									rightIcon={<ArrowAngleIcon />}>
									View More articles
								</Button>
							</div>

							{/* Articles Grid */}
							<div className="grid grid-cols-3  gap-5">
								{technologyData.map((article) => (
									<ArticleCardInsights
										key={article.id}
										{...article}
										onReadMore={handleReadMore}
									/>
								))}
							</div>
						</div>
						<div className="w-full border-t-[1.5px] border-[#E4E4E7]"></div>

						<div>
							{/* Section Header */}
							<div className="flex items-start justify-between mb-12">
								<div>
									<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
										Agriculture
									</h2>
									<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
										Stay updated with the latest career advice, industry trends,
										and <br /> professional development resources
									</p>
								</div>

								{/* View More Button */}
								<Button
									onClick={handleViewMore}
									variant="outline"
									size="md"
									rightIcon={<ArrowAngleIcon />}>
									View More articles
								</Button>
							</div>

							{/* Articles Grid */}
							<div className="grid grid-cols-3  gap-5">
								{technologyData.map((article) => (
									<ArticleCardInsights
										key={article.id}
										{...article}
										onReadMore={handleReadMore}
									/>
								))}
							</div>
						</div>
						<div className="w-full border-t-[1.5px] border-[#E4E4E7]"></div>

						<div>
							{/* Section Header */}
							<div className="flex items-start justify-between mb-12">
								<div>
									<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
										Healthcare
									</h2>
									<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
										Stay updated with the latest career advice, industry trends,
										and <br /> professional development resources
									</p>
								</div>

								{/* View More Button */}
								<Button
									onClick={handleViewMore}
									variant="outline"
									size="md"
									rightIcon={<ArrowAngleIcon />}>
									View More articles
								</Button>
							</div>

							{/* Articles Grid */}
							<div className="grid grid-cols-3  gap-5">
								{technologyData.map((article) => (
									<ArticleCardInsights
										key={article.id}
										{...article}
										onReadMore={handleReadMore}
									/>
								))}
							</div>
						</div>
						<div className="w-full border-t-[1.5px] border-[#E4E4E7]"></div>

						<div>
							{/* Section Header */}
							<div className="flex items-start justify-between mb-12">
								<div>
									<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
										Energy
									</h2>
									<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
										Stay updated with the latest career advice, industry trends,
										and <br /> professional development resources
									</p>
								</div>

								{/* View More Button */}
								<Button
									onClick={handleViewMore}
									variant="outline"
									size="md"
									rightIcon={<ArrowAngleIcon />}>
									View More articles
								</Button>
							</div>

							{/* Articles Grid */}
							<div className="grid grid-cols-3  gap-5">
								{technologyData.map((article) => (
									<ArticleCardInsights
										key={article.id}
										{...article}
										onReadMore={handleReadMore}
									/>
								))}
							</div>
						</div>
						<div className="w-full border-t-[1.5px] border-[#E4E4E7]"></div>

						<div>
							{/* Section Header */}
							<div className="flex items-start justify-between mb-12">
								<div>
									<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
										Education
									</h2>
									<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
										Stay updated with the latest career advice, industry trends,
										and <br /> professional development resources
									</p>
								</div>

								{/* View More Button */}
								<Button
									onClick={handleViewMore}
									variant="outline"
									size="md"
									rightIcon={<ArrowAngleIcon />}>
									View More articles
								</Button>
							</div>

							{/* Articles Grid */}
							<div className="grid grid-cols-3  gap-5">
								{technologyData.map((article) => (
									<ArticleCardInsights
										key={article.id}
										{...article}
										onReadMore={handleReadMore}
									/>
								))}
							</div>
						</div>
						<div className="w-full border-t-[1.5px] border-[#E4E4E7]"></div>

						<div>
							{/* Section Header */}
							<div className="flex items-start justify-between mb-12">
								<div>
									<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
										Arts & Culture
									</h2>
									<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
										Stay updated with the latest career advice, industry trends,
										and <br /> professional development resources
									</p>
								</div>

								{/* View More Button */}
								<Button
									onClick={handleViewMore}
									variant="outline"
									size="md"
									rightIcon={<ArrowAngleIcon />}>
									View More articles
								</Button>
							</div>

							{/* Articles Grid */}
							<div className="grid grid-cols-3  gap-5">
								{technologyData.map((article) => (
									<ArticleCardInsights
										key={article.id}
										{...article}
										onReadMore={handleReadMore}
									/>
								))}
							</div>
						</div>
					</div>
				) : activeTab === "Articles" ? (
					<div className="w-full grid grid-cols-3 gap-5">
						{articlesTabData.map((article, index) => (
							<ArticleCardInsights {...article} key={index} />
						))}
					</div>
				) : activeTab === "Corporate Notices" ? (
					<section className="grid grid-cols-[22%_78%] gap-5">
						<aside>
							<FilterSidebar sections={buildFilterSections()} />
						</aside>

						<div className="grid grid-cols-3 gap-5">
							{corporateNotices.map((notice) => (
								<CorporateNoticesCard
									key={notice.id}
									title={notice.title}
									description={notice.description}
									organization={notice.organization}
									postedDate={notice.postedDate}
									onClick={() =>
										router.push(
											`/insights-resources/corporate-notices/${notice.id}`
										)
									}
								/>
							))}
						</div>
					</section>
				) : null}
			</section>
			<Footer />
		</main>
	);
}
