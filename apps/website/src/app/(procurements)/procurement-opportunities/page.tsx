"use client";

import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import OpportunityCard from "@/components/ui/OpportunityCard";
import { Button } from "@/components/ui";
import {
	ArrowAngleIcon,
	ConstructionIcon,
	ITServicesIcon,
	HealthcareIcon,
	ConsultancyIcon,
	EnergyIcon,
	AgricultureIcon,
	LogisticsIcon,
	EducationIcon,
} from "@/components/common/icons";

import Image from "next/image";
import { ReactNode } from "react";
import { useRouter, usePathname } from "next/navigation";
import { opportunitiesData } from "@/lib/opportunities";

interface CategoryCardProps {
	icon: ReactNode;
	title: string;
	subtitle: string;
}

const opportunityCategories = [
	{
		icon: <ConstructionIcon />,
		title: "Construction",
		subtitle: "Building and infrastructure development projects",
	},
	{
		icon: <ITServicesIcon />,
		title: "IT Services",
		subtitle: "Technology, software and digital solutions",
	},
	{
		icon: <HealthcareIcon />,
		title: "Healthcare",
		subtitle: "Medical equipment, pharmaceuticals",
	},
	{
		icon: <ConsultancyIcon />,
		title: "Consultancy",
		subtitle: "Professional advisory and consulting services",
	},
	{
		icon: <EnergyIcon />,
		title: "Energy",
		subtitle: "Power generation, distribution and renewable energy",
	},
	{
		icon: <AgricultureIcon />,
		title: "Agriculture",
		subtitle: "Agricultural products, farming and food security",
	},
	{
		icon: <LogisticsIcon />,
		title: "Logistics",
		subtitle: "Transportation, shipping and supply chain",
	},
	{
		icon: <EducationIcon />,
		title: "Education",
		subtitle: "Educational services, training and capacity building",
	},
];

const CategoryCard = ({ icon, title, subtitle }: CategoryCardProps) => (
	<div className="bg-white py-[32px] px-5 rounded-2xl flex items-center gap-4 border-[0.75] border-[#E4E4E7]">
		<div className="border-[0.3px] border-[#C0CCFF] bg-[#EBEFFF] rounded-full p-[14px]">
			{icon}
		</div>

		<div>
			<h3 className="font-semibold text-[18px] leading-[24px] text-[#27272A] mb-1">
				{title}
			</h3>
			<p className="font-semibold text-[14px] leading-[22px] text-[#71717A] ">
				{subtitle}
			</p>
		</div>
	</div>
);
export default function ProcurementOpportunitiesPage() {
	const handleBookmark = (id: string) => {
		console.log("Bookmark opportunity:", id);
		// Implement bookmark functionality
	};

	const handleShare = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	const handleViewDetails = (id: string) => {
		router.push(`/procurement-opportunities/${id}`);
	};

	const handleViewMore = () => {
		console.log("View more opportunities");
		// Implement navigation to opportunities page
	};

	const router = useRouter();
	const pathname = usePathname();

	const handleBrowseOpportunities = () => {
		const currentPath = pathname;

		router.push(`${currentPath}/browse`);
	};

	return (
		<div className="min-h-screen flex flex-col bg-[#FAFAFA]">
			<Header />
			<main className="flex-1 w-full">
				{/* Top Section */}
				<section className="mx-auto">
					<div className="flex flex-col px-4 py-8 md:flex-row md:items-center md:justify-between gap-6 md:gap-[58px] md:px-[80px] md:py-[60px]">
						<div className="w-full">
							<h1 className="text-[36px] leading-[44px] md:text-[48px] mdleading-[64px] font-regular text-[#27272A] mb-3 md:mb-5">
								Find and Post Procurement <br />
								Opportunities
							</h1>
							<p className="text-[#A1A1AA] text-base md:text-[20px] font-semibold leading-[26px] mb-[18px] md:mb-[30px]">
								Connect with quality vendors and businesses. Post your <br className="hidden md:block" />{" "}
								procurement needs or find relevant opportunities all in one
								place.
							</p>
							<div className="flex items-center gap-5 mb-5">
								<Button
									variant="primary"
									size="md"
									shadow
									className="hidden md:block"
									onClick={handleBrowseOpportunities}>
									Browse Opportunities
								</Button>
								<Button
									variant="outline"
									size="md"
									shadow
									className="hidden md:block"
									rightIcon={<ArrowAngleIcon />}>
									Post an opportunity
								</Button>
								<Button
									variant="primary"
									size="xs"
									shadow
									className="md:hidden"
									onClick={handleBrowseOpportunities}>
									Browse Opportunities
								</Button>
								<Button
									variant="outline"
									size="xs"
									shadow
									className="md:hidden"
									rightIcon={<ArrowAngleIcon />}>
									Post an opportunity
								</Button>
							</div>
						</div>
						<div className=" w-full h-[176px] md:h-[313px] rounded-xl overflow-hidden relative">
							<Image
								src="/procurement-img.png"
								alt="Job search"
								fill
								className="object-cover"
							/>
						</div>
					</div>
				</section>
				{/* Main Content */}
				<section className="px-2 py-12 md:px-[90px] md:py-[120px] bg-white">
					<div>
						{/* Section Header */}
						<div className="flex flex-col md:flex-row gap-6 md:gap-auto items-start justify-between mb-8 md:mb-12">
							<div>
								<h2 className="text-[#27272A] font-semibold text-[24px] leading-[32px] md:text-[48px] md:leading-[56px] mb-3 md:mb-6">
									Featured Opportunities
								</h2>
								<p className="text-neutral-600 font-400 text-base md:text-[24px] md:leading-[32px]">
									DExplore the latest and most relevant procurement
									opportunities
								</p>
							</div>

							{/* View More Button */}
							<Button
								onClick={handleViewMore}
								variant="outline"
								className="hidden md:block"
								rightIcon={<ArrowAngleIcon />}
								shadow
								size="md">
								View More
							</Button>
							<Button
								onClick={handleViewMore}
								variant="outline"
								className="md:hidden"
								rightIcon={<ArrowAngleIcon />}
								shadow
								size="xs">
								View More
							</Button>
						</div>

						{/* Opportunities Grid */}
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
							{opportunitiesData.map((opportunity) => (
								<OpportunityCard
									key={opportunity.id}
									{...opportunity}
									onBookmark={handleBookmark}
									onShare={handleShare}
									onViewDetails={() => handleViewDetails(opportunity.id)}
									btnText="Apply now"
								/>
							))}
						</div>
					</div>
				</section>

				<section className="w-full px-[90px] py-[120px] flex flex-col gap-[72px] bg-[#F4F4F5] ">
					<div className="text-center">
						<h2 className="font-semibold text-[48px] mb-6 leading-[56px] text-[#3F3F46]">
							Browse by Category
						</h2>
						<p className="font-regular text-[24px] leading-[32px] text-neutral-600">
							Find opportunities in your field of expertise
						</p>
					</div>

					<div className="grid grid-cols-4 gap-5">
						{opportunityCategories.map((category, index) => (
							<CategoryCard key={index} {...category} />
						))}
					</div>
				</section>
				{/* Newsletter & Footer */}
				<Footer />
			</main>
		</div>
	);
}
