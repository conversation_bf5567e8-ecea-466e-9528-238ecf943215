"use client";
import {
	ArrowRightIcon,
	ShareIcon,
	CalendarIcon,
	BudgetIcon,
	ClockIcon,
	SuitcaseIconNew,
	LocationIcon,
	MailIcon,
	PhoneIcon,
	DeadlineIcon,
	CalendarCheckIcon,
	FileIcon,
	DownloadIcon,
	OfficeIcon,
} from "@/components/common/icons";
import { Bookmark } from "lucide-react";
import Header from "@/components/layout/Header";
import { opportunitiesData } from "@/lib/opportunities";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useParams } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui";
import OpportunityCard from "@/components/ui/OpportunityCard";
import Footer from "@/components/layout/Footer";

const SubmissionDetailsCard = () => {
	return (
		<div className="px-6 py-8 rounded-[16px] border-[0.75px] border-[#E4E4E7] bg-white flex flex-col gap-[14px] h-fit">
			<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
				Submission Details
			</p>
			<div className="">
				<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
					Deadline
				</p>
				<div className="flex items-center gap-2">
					<CalendarIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						May 15, 2025 <span className="text-[#F17171]">(68 days left)</span>
					</span>
				</div>
			</div>

			<div className="w-full border-t-[1.5px] border-[#F4F4F5]"></div>

			<div className="">
				<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
					Organization
				</p>
				<div className="flex items-center gap-2">
					<OfficeIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						Ministry of Technology
					</span>
				</div>
			</div>

			<div className="w-full border-t-[1.5px] border-[#F4F4F5]"></div>

			<div className="">
				<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
					Contact Information
				</p>
				<div className="flex items-center gap-2">
					<MailIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						<EMAIL>
					</span>
				</div>
				<div className="flex items-center gap-2">
					<PhoneIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						+****************
					</span>
				</div>
			</div>
		</div>
	);
};

export default function OpportunityDetailPage() {
	const params = useParams();
	const { id } = params;

	const opportunity = opportunitiesData.find((opp) => opp.id === id);

	console.log(opportunity);

	if (!opportunity) {
		return (
			<div className="min-h-screen flex flex-col items-center justify-center">
				<h1 className="text-3xl font-bold mb-4">Opportunity Not Found</h1>
				<p className="text-lg">
					No opportunity found for ID: <span className="font-mono">{id}</span>
				</p>
			</div>
		);
	}

	const handleBookmark = (id: string) => {
		console.log("Bookmark opportunity:", id);
		// Implement bookmark functionality
	};

	const handleShare = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};
	const handleViewMore = () => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};
	const handleViewDetails = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	return (
		<main className="w-full h-full">
			<Header />

			<section className="w-full px-[90px] py-[32px] bg-[#FAFAFA] h-full">
				<Link
					href="/procurement-opportunities"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] mb-[32px] hover:text-brand-500 transition-all duration-300 ">
					<ArrowRightIcon className="rotate-180 w-6 h-6 hover:text-brand-500" color="#52525B" /> Back
					to Opportunities
				</Link>

				<div className="w-full bg-[#FAFAFA] h-full">
					<div
						className=" bg-[#FAFAFA]  transition-shadow duration-300 flex flex-col gap-4 w-full"
						// style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}
					>
						{/* Header with Tags and Actions */}
						<div className="flex items-start justify-between mb-2 sm:mb-4 flex-wrap gap-y-2">
							{/* Tags */}
							<div className="flex gap-2 flex-wrap">
								{opportunity.tags.map((tag, index) => (
									<span
										key={index}
										className={cn(
											tag.toLowerCase() === "new"
												? "text-[#F17171] border-[#FDE7E7] border bg-[#FEF6F6]"
												: "text-neutral-500 border-neutral-200 border bg-neutral-100",
											"px-2 sm:px-3 py-0.5 sm:py-1 text-xs sm:text-sm leading-[18px] sm:leading-[22px] rounded-full font-medium"
										)}>
										{tag}
									</span>
								))}
							</div>

							{/* Action Icons */}
							<div className="flex items-center gap-2 sm:gap-3">
								<button
									onClick={() => handleBookmark(opportunity.id)}
									className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
									aria-label="Share opportunity">
									<ShareIcon />
								</button>
								<div className="w-[1px] h-4 bg-[#E4E4E7]"></div>
								<button
									onClick={() => handleShare(opportunity.id)}
									className={`p-2 hover:bg-gray-100 rounded-lg transition-colors ${
										opportunity.isBookmarked
											? "text-primary-500"
											: "text-gray-500"
									}`}
									aria-label={
										opportunity.isBookmarked
											? "Remove bookmark"
											: "Bookmark opportunity"
									}>
									<Bookmark
										className={`w-6 h-6 text-neutral-400 ${
											opportunity.isBookmarked ? "fill-current" : ""
										}`}
									/>
								</button>
							</div>
						</div>

						{/* Company Info */}
						<div className="flex items-center gap-2.5 sm:gap-3.5 mb-2 sm:mb-4">
							<div className="w-10 h-10 sm:w-12 sm:h-12 relative rounded-[4px] overflow-hidden bg-white border border-neutral-200 flex items-center justify-center">
								<Image
									src={opportunity.companyLogo}
									alt={`${opportunity.company} logo`}
									fill
									className="object-cover"
								/>
							</div>
							<div>
								<p className="text-neutral-500 text-xs sm:text-[20px] leading-[26px] sm:leading-[26px] font-regular">
									{opportunity.company}
								</p>
							</div>
						</div>
						<h2 className="font-regular text-[#18181B] text-[48px] sm:text-[48px] leading-5 sm:leading-[64px] max-w-[833px]">
							{opportunity.title}
						</h2>
						<p className="text-[20px] leading-[26px] font-regular text-[#71717A] max-w-[833px]">
							{opportunity?.subtitle}
						</p>

						<div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
							{/* Job Details */}
							<div className="flex items-center gap-2 text-xs sm:text-sm text-neutral-500 font-500">
								<div className="flex items-center gap-1">
									<Image
										src="/nigeria-flag.png"
										alt="country flag"
										width={24}
										height={18}
										className="rounded-[4px]"
									/>

									<span className="text-[#71717A] font-medium text-sm leading-[22px]">
										{opportunity.location}
									</span>
								</div>
								{opportunity.isFulltime && (
									<div className="flex items-center gap-1">
										<SuitcaseIconNew />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											{opportunity.workType}
										</span>
									</div>
								)}
								{opportunity.showTime && (
									<div className="flex items-center gap-1">
										<ClockIcon />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											{opportunity.timezone}
										</span>
									</div>
								)}
								{opportunity.showBudget && (
									<div className="flex items-center gap-1">
										<BudgetIcon />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											$500k - $1M
										</span>
									</div>
								)}
								{opportunity.date && (
									<div className="flex items-center gap-1">
										<CalendarIcon color="#A1A1AA" />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											{opportunity.date}
										</span>
									</div>
								)}
								{opportunity.deadline && (
									<div className="flex items-center gap-1">
										<ClockIcon />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											{opportunity.deadline}{" "}
											<span className="text-[#F17171]">
												({opportunity.deadlineCounter + "days left"})
											</span>
										</span>
									</div>
								)}
							</div>

							{/* View Details Button */}
						</div>
					</div>

					<div className="grid grid-cols-3 gap-y-6 p-5 mt-10 bg-white">
						{/* details cards */}
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<LocationIcon />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Location
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									San Fransisco, CA
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<MailIcon />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Mail
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									<EMAIL>
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<PhoneIcon />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Phone number
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									+****************
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<BudgetIcon color="#52525B" />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Amount
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									$500k - $1M
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<DeadlineIcon />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Deadline
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#ED4242]">
									April 30, 2025
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<CalendarCheckIcon />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Date Posted
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									1 day ago
								</p>
							</div>
						</div>
					</div>

					<div className="w-full border-t-[1.5px] border-[#E4E4E7] my-10"></div>

					<div className="grid grid-cols-[70%_30%] gap-[42px]">
						<div className="flex flex-col gap-8">
							<div>
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									Description
								</p>
								<p className="text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
									{opportunity.description}
								</p>
							</div>

							<div>
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									Eligibility Criteria
								</p>
								<ul className=" ">
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Minimum 5 years of experience in IT services
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Previous experience with government systems
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* ISO 27001 certification
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Local presence with 24/7 support capability
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Financial stability and capacity to handle projects of
										this scale
									</li>
								</ul>
							</div>
							<div className="border border-[#E4E4E7] p-4 rounded-[16px] flex flex-col gap-3.5">
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									Attachments
								</p>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Terms of Reference.pdf
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												1.2 MB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Technical Requirements.docx
											</p>
											<p className="text-sm leading-[22px]  font-medium text-[#71717A]">
												920 KB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Proposal Template.docx
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												420 KB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>
						<aside>
							<SubmissionDetailsCard />

							<Button
								variant="primary"
								className="w-full mt-4 focus:outline-none shadow-none uppercase">
								Place bid
							</Button>
						</aside>
					</div>
				</div>
			</section>

			<section className="px-[90px] py-[120px] bg-white">
				<div>
					{/* Section Header */}
					<div className="flex items-start justify-between mb-12">
						<div>
							<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
								Featured Opportunities
							</h2>
							<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
								DExplore the latest and most relevant procurement opportunities
							</p>
						</div>

						{/* View More Button */}
						<Button onClick={handleViewMore} variant="primary" size="md">
							View More
						</Button>
					</div>

					{/* Opportunities Grid */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-5">
						{opportunitiesData.map((opportunity) => (
							<OpportunityCard
								key={opportunity.id}
								{...opportunity}
								onBookmark={handleBookmark}
								onShare={handleShare}
								onViewDetails={() => handleViewDetails(opportunity.id)}
								btnText="Apply now"
							/>
						))}
					</div>
				</div>
			</section>
			<Footer />
		</main>
	);
}
