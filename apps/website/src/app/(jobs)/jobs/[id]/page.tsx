"use client";
import {
	ArrowRightIcon,
	ShareIcon,
	ClockIcon,
	DeadlineIcon,
	CalendarCheckIcon,
	LocationIcon,
	SuitcaseIconNew,
} from "@/components/common/icons";
import Header from "@/components/layout/Header";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useParams } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui";
import Footer from "@/components/layout/Footer";
// import { jobs } from "../page";
import { Bookmark, BookmarkIcon } from "lucide-react";
import OpportunityCard from "@/components/ui/OpportunityCard";

// {
//     id: "1",
//     title: "African Leadership Academy Scholarship",
//     company: "University of Lagos",
//     companyLogo: "/scholarship-img1.png",
//     location: "Lagos, NG",
//     workType: "Full-time",
//     timezone: "GMT +1",
//     tags: [
//         {
//             label: "Undergraduate",
//             bgColor: "bg-[#F4EBF8]",
//             textColor: "text-[#8F34B4]",
//             borderColor: "border-[#F4EBF8]",
//         },
//     ],
//     isBookmarked: false,
//     showTime: false,
//     showBudget: true,
//     budget: "Amount: Full Tuition",
//     isFulltime: false,
//     showDate: true,
//     date: "Deadline: Apr 2, 2025",
//     isBookmarkIcon: false,
// },

interface IJob {
	id: string;
	title: string;
	company: string;
	companyLogo: string;
	location: string;
	workType: string;
	timezone: string;
	tags: string[];
	isBookmarked: boolean;
	showTime: boolean;
	city: string;
	isFulltime: boolean;
	time: string;
}

const SubmissionDetailsCard = () => {
	return (
		<div className="md:px-6 md:py-8 py-12 md:rounded-[16px] md:border-[0.75px] md:border-[#E4E4E7] md:bg-white flex flex-col gap-[14px] h-fit">
			<p className="font-semibold text-base md:text-[24px] text-[#52525B] leading-[22px]">
				About Google
			</p>

			<div className="">
				{/* <p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
					Scholarship Body
				</p> */}
				<div className="flex items-center gap-2">
					{/* <OfficeIcon /> */}
					<span className="text-[#A1A1AA] font-regular text-base md:font-medium md:text-sm leading-[22px]">
						At Google, we believe great ideas can come from anywhere. We care
						deeply about creating products that are helpful, equitable, and
						designed for everyone.
					</span>
				</div>
			</div>

			<div className="flex flex-col gap-4 ">
				<div className="grid grid-cols-[30%_70%]">
					<p className="text-[#A1A1AA] text-base font-regular md:font-medium md:text-sm leading-[22px]">
						Website
					</p>
					<p className="text-[#335CFF] text-base font-regular md:font-medium md:text-sm  leading-[22px]">
						google.com
					</p>
				</div>
				<div className="grid grid-cols-[30%_70%]">
					<p className="text-[#A1A1AA] text-base font-regular md:font-medium md:text-sm  leading-[22px]">
						Industry
					</p>
					<p className="text-[#71717A] text-base font-regular md:font-medium md:text-sm  leading-[22px]">
						Technology, innovation, scalable systems
					</p>
				</div>
				<div className="grid grid-cols-[30%_70%]">
					<p className="text-[#A1A1AA] text-base font-regular md:font-medium md:text-sm  leading-[22px]">
						Founded
					</p>
					<p className="text-[#71717A] text-base font-regular md:font-medium md:text-sm  leading-[22px]">
						1990
					</p>
				</div>
				<div className="grid grid-cols-[30%_70%]">
					<p className="text-[#A1A1AA] text-base font-regular md:font-medium md:text-sm  leading-[22px]">
						Size
					</p>
					<p className="text-[#71717A] text-base font-regular md:font-medium md:text-sm  leading-[22px]">
						10K - 15K employees
					</p>
				</div>
			</div>
		</div>
	);
};

const opportunitiesData = [
	{
		id: "1",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
	},
	{
		id: "2",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
	},
	{
		id: "3",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
	},
	{
		id: "4",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
	},
];

const jobs: IJob[] = [
	{
		id: "1",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		city: "San Fransisco, CA",
		isFulltime: true,
		time: "remote",
	},
	{
		id: "2",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		city: "San Fransisco, CA",
		isFulltime: true,
		time: "remote",
	},
	{
		id: "3",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		city: "San Fransisco, CA",
		isFulltime: true,
		time: "remote",
	},
	{
		id: "4",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		city: "San Fransisco, CA",
		isFulltime: true,
		time: "remote",
	},
	{
		id: "5",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		city: "San Fransisco, CA",
		isFulltime: true,
		time: "remote",
	},
	{
		id: "6",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		city: "San Fransisco, CA",
		isFulltime: true,
		time: "remote",
	},
	{
		id: "7",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		city: "San Fransisco, CA",
		isFulltime: true,
		time: "remote",
	},
	{
		id: "8",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		city: "San Fransisco, CA",
		isFulltime: true,
		time: "remote",
	},
	// ...repeat or map for demo
];

export default function JobOpportunityDetailsPage() {
	const params = useParams();
	const { id } = params;

	const job = jobs.find((job) => job.id === id);

	console.log(job);

	if (!job) {
		return (
			<div className="min-h-screen flex flex-col items-center justify-center">
				<h1 className="text-3xl font-bold mb-4">Job Not Found</h1>
				<p className="text-lg">
					No job found for ID: <span className="font-mono">{id}</span>
				</p>
			</div>
		);
	}

	const handleBookmark = (id: string) => {
		console.log("Bookmark opportunity:", id);
		// Implement bookmark functionality
	};

	const handleShare = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};
	const handleViewMore = () => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};
	const handleViewDetails = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	return (
		<main className="w-full h-full">
			<Header />

			<section className="w-full  md:px-[90px] md:py-[32px] bg-white md:bg-[#FAFAFA] h-full">
				<Link
					href="/jobs"
					className=" p-4 md:p-0  flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] md:mb-[32px] hover:text-brand-500 transition-all duration-300 ">
					<ArrowRightIcon
						className="rotate-180 w-6 h-6 hover:text-brand-500"
						color="#52525B"
					/>{" "}
					Back to Opportunities
				</Link>

				<div className="w-full bg-[#FAFAFA] h-full">
					<div className="px-4 md:hidden bg-white pb-4">
						<div className="p-2 bg-[#FAFAFA] rounded-[8px] flex flex-col gap-3 md:hidden">
							<h2 className="font-regular text-[#18181B] text-[20px] leading-[26px] ">
								{job.title}
							</h2>

							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<div className="w-[30px] h-[30px] relative rounded-[5px] p-2 overflow-hidden bg-white border border-neutral-200 flex items-center justify-center">
										<Image
											src={job.companyLogo}
											alt={`${job.company} logo`}
											fill
											className="object-cover"
										/>
									</div>
									<p className="text-neutral-500 font-regular text-[20px] leading-[26px] font-regular">
										{job.company}
									</p>
								</div>

								<div className="flex items-center gap-4 ml-auto">
									<ShareIcon color="#A1A1AA" />
									<span className="h-[25px] border-[1.5px] border-[#E4E4E7]"></span>
									<BookmarkIcon className="w-6 h-6 text-[#A1A1AA]" />
								</div>
							</div>
						</div>
					</div>
					<div
						className=" md:bg-[#FAFAFA] hidden  transition-shadow duration-300 md:flex flex-col gap-4 w-full"
						// style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}
					>
						{/* Header with Tags and Actions */}
						<div className="hidden md:flex items-start justify-between mb-2 sm:mb-4 flex-wrap gap-y-2  ">
							{/* Tags */}
							<div className="flex gap-2 flex-wrap">
								{job.tags.map((tag, index) => (
									<span
										key={index}
										className={cn(
											tag.toLowerCase() === "new"
												? "text-[#F17171] border-[#FDE7E7] border bg-[#FEF6F6]"
												: "text-neutral-500 border-neutral-200 border bg-neutral-100",
											"px-2 sm:px-3 py-0.5 sm:py-1 text-xs sm:text-sm leading-[18px] sm:leading-[22px] rounded-full font-medium"
										)}>
										{tag}
									</span>
								))}
							</div>

							{/* Action Icons */}
							<div className="ml-auto flex items-center gap-2 sm:gap-3">
								<button
									onClick={() => handleBookmark(job.id)}
									className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
									aria-label="Share opportunity">
									<ShareIcon />
								</button>
								<div className="w-[1px] h-4 bg-[#E4E4E7]"></div>
								<button
									onClick={() => handleShare(job.id)}
									className={`p-2 hover:bg-gray-100 rounded-lg transition-colors ${
										job.isBookmarked ? "text-primary-500" : "text-gray-500"
									}`}
									aria-label={
										job.isBookmarked
											? "Remove bookmark"
											: "Bookmark opportunity"
									}>
									<Bookmark
										className={`w-6 h-6 text-neutral-400 ${
											job.isBookmarked ? "fill-current" : ""
										}`}
									/>
								</button>
							</div>
						</div>

						{/* Company Info */}
						<div className="flex items-center gap-2.5 sm:gap-3.5 ">
							<div className="w-10 h-10 sm:w-12 sm:h-12 relative rounded-[8px] p-2 overflow-hidden bg-white border border-neutral-200 flex items-center justify-center">
								<Image
									src={job.companyLogo}
									alt={`${job.company} logo`}
									fill
									className="object-cover"
								/>
							</div>
							<div>
								<p className="text-neutral-500 text-xs sm:text-[20px] leading-[26px] sm:leading-[26px] font-regular">
									{job.company}
								</p>
							</div>
						</div>
						<h2 className="font-regular text-[#18181B] text-[48px] sm:text-[48px] leading-5 sm:leading-[64px] max-w-[833px]">
							{job.title}
						</h2>

						<div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
							{/* Job Details */}
							<div className="flex items-center gap-2 text-xs sm:text-sm text-neutral-500 font-500">
								<div className="flex items-center gap-1">
									<LocationIcon />

									<span className="text-[#71717A] font-medium text-sm leading-[22px]">
										{job.city}
									</span>
								</div>
								{job.isFulltime && (
									<div className="flex items-center gap-1">
										<SuitcaseIconNew />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											{job.workType}
										</span>
									</div>
								)}
								{job.showTime && (
									<div className="flex items-center gap-1">
										<ClockIcon />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											{job.time}
										</span>
									</div>
								)}
							</div>

							{/* View Details Button */}
						</div>
					</div>

					<div className="w-full border-t-[1.5px] border-[#E4E4E7] pt-4 bg-white md:bg-none md:pt-0 md:my-10"></div>

					{/* white cards section */}
					<div className="grid grid-cols-2 md:grid-cols-3 gap-y-6 p-5 md:mt-6 bg-white border-y border-[#F4F4F5] md:border-0">
						{/* details cards */}
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<SuitcaseIconNew />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Job Type
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									Full Time
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<LocationIcon />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Location
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									San Fransisco, CA
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<ClockIcon />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Time Zone
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									GMT +1
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<SuitcaseIconNew />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Salary
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									$120K-200K
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<CalendarCheckIcon color="#52525B" />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Date Posted
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#71717A]">
									1 day ago
								</p>
							</div>
						</div>
						<div className="flex items-start gap-2 ">
							<div className="p-2 rounded-full border border-[#E4E4E7]">
								<DeadlineIcon color="#52525B" />
							</div>
							<div className="">
								<p className="text-sm leading-[22px] font-medium text-[#18181B]">
									Deadline
								</p>
								<p className="text-sm leading-[22px] font-medium text-[#ED4242]">
									April 30, 2025
								</p>
							</div>
						</div>
					</div>

					<div className="bg-white py-4 md:hidden px-4">
						<Button variant="primary" size="sm" className="w-full">
							LOGIN TO APPLY
						</Button>
					</div>

					<div className="grid md:grid-cols-[67%_30%] gap-[42px] w-full mt-10 px-4 md:px-0 ">
						<div className="flex flex-col gap-10">
							<div>
								<p className="font-semibold text-base md:text-[24px] text-[#52525B] leading-[22px]">
									About the Role
								</p>
								<p className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
									We’re looking for a Senior UX Designer to join our
									multidisciplinary UX team at Google. In this role, you’ll
									drive the end-to-end user experience for products that impact
									millions of users worldwide. From research and ideation to
									execution and iteration, you’ll help craft thoughtful,
									human-centered experiences that solve real-world problems.
									You’ll work closely with product managers, engineers,
									researchers, and other designers across Google to turn complex
									challenges into intuitive, accessible, and delightful user
									experiences.
								</p>
							</div>

							<div>
								<p className="font-semibold text-base md:text-[24px] text-[#52525B] leading-[22px]">
									Responsibilities
								</p>
								<ul className=" ">
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Lead the UX strategy, design, and execution for key Google
										products.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Collaborate with cross-functional teams to define user
										problems and design elegant solutions.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Conduct and synthesize user research, usability testing,
										and data analysis to inform design decisions.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Translate insights into wireframes, prototypes, and
										high-fidelity visuals that communicate design intent
										clearly.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Advocate for accessibility, inclusivity, and design best
										practices across the product lifecycle.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Mentor junior designers and contribute to the overall
										growth of the UX team.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Present design work to stakeholders and effectively
										articulate design rationale.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Stay current on industry trends, emerging technologies,
										and design tools.
									</li>
								</ul>
							</div>
							<div>
								<p className="font-semibold text-base md:text-[24px] text-[#52525B] leading-[22px]">
									Who you are
								</p>
								<ul className=" ">
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* A senior-level designer with 6+ years of experience
										crafting user-centered digital products.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Skilled in UX design, interaction design, and visual
										design.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Adept at using Figma and other prototyping tools to create
										compelling experiences.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Experienced in working with design systems and
										contributing to their evolution.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* A strategic thinker who balances user needs with business
										goals.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* A great communicator with strong storytelling and
										presentation skills.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* A great communicator with strong storytelling and
										presentation skills.
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Familiar with front-end technologies (HTML/CSS/JavaScript)
										is a plus.
									</li>
								</ul>
							</div>
							<div>
								<p className="font-semibold text-base md:text-[24px] text-[#52525B] leading-[22px]">
									What we offer
								</p>
								<ul className=" ">
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Competitive salary and bonus structure
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Hybrid working model with flexible hours
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Generous learning and development budget
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Annual wellness stipend and mental health support
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Inclusive and diverse workplace culture
									</li>
									<li className="text-base md:text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Opportunity to shape products used by billions globally
									</li>
								</ul>
							</div>
						</div>
						<aside className="max-w-30% bg-greeen-200">
							<SubmissionDetailsCard />

							<Button
								variant="primary"
								className="w-full mt-4 focus:outline-none shadow-none font-semibold uppercase hidden md:block">
								Login to Apply
							</Button>
						</aside>
					</div>
				</div>
			</section>

			<section className="bg-white px-[90px] py-[120px] hidden md:block ">
				<div>
					{/* Section Header */}
					<div className="flex items-start justify-between mb-12">
						<div>
							<h2 className="text-neutral-800 font-regular text-[48px] leading-[64px] mb-6">
								Featured Opportunities
							</h2>
						</div>

						{/* View More Button */}
						<Button onClick={handleViewMore} variant="primary" size="md">
							View More
						</Button>
					</div>

					{/* Opportunities Grid */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-5">
						{opportunitiesData.map((opportunity) => (
							<OpportunityCard
								key={opportunity.id}
								{...opportunity}
								onBookmark={handleBookmark}
								onShare={handleShare}
								onViewDetails={handleViewDetails}
							/>
						))}
					</div>
				</div>
			</section>

			<Footer />
		</main>
	);
}
