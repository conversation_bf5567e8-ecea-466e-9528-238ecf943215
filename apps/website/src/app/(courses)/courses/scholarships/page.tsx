"use client";

import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import OpportunityCard from "@/components/ui/OpportunityCard";
import { LabelInput, Tab } from "@/components/ui";
import { ArrowRightIcon, EmptyStateIcon } from "@/components/common/icons";

import Image from "next/image";
import { useState } from "react";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";
import EmptyState from "@/components/ui/EmptyState";
import { usePathname, useRouter } from "next/navigation";
import { scholarshipsData } from "@/lib/courses";

const tabs = [
	"All",
	"African Scholarships",
	"Internatiional Scholarships",
	"Asian Scholarships",
	"European Scholarships",
	"American Scholarships",
];

const filters = {
	category: [
		"Business",
		"Technology",
		"Healthcare",
		"Agriculture",
		"Education",
		"Marketing",
		"Design",
		"Language",
	],
};

export default function ScholarshipsPage() {
	const [selectedCountries, setSelectedCountries] = useState<string[]>([]);
	const [activeTab, setActiveTab] = useState("All");
	const router = useRouter();
	const pathname = usePathname();

	const handleBookmark = (id: string) => {
		console.log("Bookmark opportunity:", id);
		// Implement bookmark functionality
	};

	const handleShare = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	const handleViewDetails = (id: string) => {
		router.push(`${pathname}/${id}`);
		console.log("View details for opportunity:", id);
		// Implement navigation to opportunity details
	};

	// const handleViewMore = () => {
	// 	console.log("View more opportunities");
	// 	// Implement navigation to opportunities page
	// };

	const buildFilterSections = (): FilterSection[] => {
		if (activeTab === "All") {
			return [
				{
					type: "checkbox",
					title: "Category",
					options: filters.category.map((c) => ({ label: c, value: c })),
					selected: selectedCountries,
					onChange: setSelectedCountries,
					placeholder: "Select category",
				},
				// {
				// 	type: "checkbox",
				// 	title: "Category",
				// 	options: filters.category.map((j) => ({ label: j, value: j })),
				// 	selected: selectedCategory,
				// 	onChange: setSelectedCategory,
				// 	placeholder: "Select category",
				// },
				// {
				// 	type: "checkbox",
				// 	title: "Working Hours",
				// 	options: filters.workingHours.map((j) => ({ label: j, value: j })),
				// 	selected: selectedWorkingHour,
				// 	onChange: setSelectedWorkingHour,
				// 	placeholder: "Select job types",
				// },
			];
		}
		return [];
	};

	return (
		<div className="min-h-screen flex flex-col bg-[#FAFAFA]">
			<Header />
			<main className="flex-1 w-full">
				{/* Tab Bar */}
				<Tab tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
				{/* Top Section */}
				<section className="mx-auto">
					<div className="flex flex-col md:flex-row sm:items-center md:justify-between gap-[58px] sm:px-[80px] sm:py-[60px]">
						<div className="w-full">
							<h1 className="text-2xl sm:text-[48px] leading-[64px] font-regular text-neutral-800 mb-5">
								Secure Scholarships and Fee waivers
							</h1>
							<p className="text-[#A1A1AA] text-[20px] font-semibold leading-[26px] mb-[30px]">
								Discover scholarship opportunities to further your education
							</p>
							<div className="flex items-center gap-5 mb-5">
								<LabelInput
									inputType="search"
									placeholder="Scholarship name or keyword"
									className="bg-[#FFFFFE] w-full"
								/>
							</div>
						</div>
						<div className="hidden md:block w-full h-[313px] rounded-xl overflow-hidden relative">
							<Image
								src="/scholarships-img.png"
								alt="Job search"
								fill
								className="object-cover"
							/>
						</div>
					</div>
				</section>
				{/* Main Content */}

				<section className="mx-auto px-[90px] py-[60px] flex gap-[48px] bg-[#F4F4F5]">
					<div className="w-fit">
						{/* Filter Sidebar */}
						<FilterSidebar
							sections={buildFilterSections()}
							onApplyFilters={() => {}}
							onClearAll={() => {
								setSelectedCountries([]);
							
							}}
						/>
					</div>
					{/* Job Grid */}

					{scholarshipsData.length === 0 ? (
						<section className="w-full m-auto">
							<EmptyState
								icon={<EmptyStateIcon />}
								title="Sorry, there are no jobs available for that search"
								subtitle="Try removing filters or changing some of your search criteria"
								onClear={() => {
									setSelectedCountries([]);
								
									// Add any other filter resets here
								}}
							/>
						</section>
					) : (
						<section className="w-full">
							<div className="flex flex-col gap-[18px]">
								{scholarshipsData.map((scholarship) => (
									<OpportunityCard
										key={scholarship.id}
										{...scholarship}
										onBookmark={handleBookmark}
										onShare={handleShare}
										onViewDetails={() => handleViewDetails(scholarship.id)}
										rightIcon={
											<ArrowRightIcon className="w-5 h-5" color="#335CFF" />
										}
										btnText="View details"
										btnSize="xs"
									/>
								))}
							</div>
						</section>
					)}
				</section>

				<Footer />
			</main>
		</div>
	);
}
