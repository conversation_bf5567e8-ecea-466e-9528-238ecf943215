"use client";
import {
	ArrowRightIcon,
	ShareIcon,
	CalendarIcon,
	DeadlineIcon,
	CalendarCheckIcon,
	FileIcon,
	DownloadIcon,
	OfficeIcon,
	GlobeIcon,
	LocationIcon,
	DoubleCheckIcon,
	PhoneIcon,
	MailIcon,
	PeopleIcon,
	AwardIcon,
} from "@/components/common/icons";
import Header from "@/components/layout/Header";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useParams } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui";
import Footer from "@/components/layout/Footer";
import { scholarshipsData } from "@/lib/courses";

// {
//     id: "1",
//     title: "African Leadership Academy Scholarship",
//     company: "University of Lagos",
//     companyLogo: "/scholarship-img1.png",
//     location: "Lagos, NG",
//     workType: "Full-time",
//     timezone: "GMT +1",
//     tags: [
//         {
//             label: "Undergraduate",
//             bgColor: "bg-[#F4EBF8]",
//             textColor: "text-[#8F34B4]",
//             borderColor: "border-[#F4EBF8]",
//         },
//     ],
//     isBookmarked: false,
//     showTime: false,
//     showBudget: true,
//     budget: "Amount: Full Tuition",
//     isFulltime: false,
//     showDate: true,
//     date: "Deadline: Apr 2, 2025",
//     isBookmarkIcon: false,
// },

const SubmissionDetailsCard = () => {
	return (
		<div className="px-6 py-8 rounded-[16px] border-[0.75px] border-[#E4E4E7] bg-white flex flex-col gap-[14px] h-fit">
			{/* <p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
				Submission Details
			</p> */}

			<div className="">
				<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
					Scholarship Body
				</p>
				<div className="flex items-center gap-2">
					<OfficeIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						African Leadership Academy Scholarship
					</span>
				</div>
			</div>

			<div className="w-full border-t-[1.5px] border-[#F4F4F5]"></div>

			<div className="">
				<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
					Deadline
				</p>
				<div className="flex items-center gap-2">
					<CalendarIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						June 30, 2025 <span className="text-[#F17171]">(68 days left)</span>
					</span>
				</div>
			</div>

			<div className="w-full border-t-[1.5px] border-[#F4F4F5]"></div>

			<div className="">
				<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
					Contact Information
				</p>
				<div className="flex items-center gap-2">
					<MailIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						<EMAIL>
					</span>
				</div>
				<div className="flex items-center gap-2">
					<PhoneIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						+****************
					</span>
				</div>
			</div>
		</div>
	);
};



export default function ScholarshipDetailsPage() {
	const params = useParams();
	const { id } = params;

	const scholarship = scholarshipsData.find(
		(scholarship) => scholarship.id === id
	);

	console.log(scholarship);

	if (!scholarship) {
		return (
			<div className="min-h-screen flex flex-col items-center justify-center">
				<h1 className="text-3xl font-bold mb-4">Scholarship Not Found</h1>
				<p className="text-lg">
					No scholarship found for ID: <span className="font-mono">{id}</span>
				</p>
			</div>
		);
	}

	// const handleBookmark = (id: string) => {
	// 	console.log("Bookmark opportunity:", id);
	// 	// Implement bookmark functionality
	// };

	// const handleShare = (id: string) => {
	// 	console.log("Share opportunity:", id);
	// 	// Implement share functionality
	// };
	// const handleViewMore = () => {
	// 	console.log("Share opportunity:", id);
	// 	// Implement share functionality
	// };
	// const handleViewDetails = (id: string) => {
	// 	console.log("Share opportunity:", id);
	// 	// Implement share functionality
	// };

	return (
		<main className="w-full h-full">
			<Header />

			<section className="w-full px-[90px] py-[32px] bg-[#FAFAFA] h-full">
				<Link
					href="/courses/scholarships"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] mb-[32px] hover:text-brand-500 transition-all duration-300 ">
					<ArrowRightIcon
						className="rotate-180 w-6 h-6 hover:text-brand-500"
						color="#52525B"
					/>{" "}
					Back to Scholarships
				</Link>

				<div className="w-full bg-[#FAFAFA] h-full">
					<div className="flex gap-2 flex-wrap">
						{scholarship.tags.map((tag, index) => {
							// New style: object tag, use custom colors if provided
							return (
								<span
									key={index}
									className={cn(
										tag.textColor || "text-neutral-500",
										tag.borderColor || "border-neutral-200",
										tag.bgColor || "bg-neutral-100",
										"px-2 sm:px-3 py-0.5 sm:py-1 text-xs sm:text-sm leading-[18px] sm:leading-[22px] rounded-full font-medium border"
									)}>
									{tag.label}
								</span>
							);
						})}
					</div>
					<div className="grid grid-cols-[70%_30%] gap-[42px] mt-4">
						<div className="flex flex-col gap-10">
							<div className="flex flex-col gap-4">
								<div className="flex gap-4 items-center">
									<div className="w-10 h-10 sm:w-12 sm:h-12 relative rounded-[8px] overflow-hidden bg-white border border-neutral-200 flex items-center justify-center">
										<Image
											src={scholarship.companyLogo}
											alt={`${scholarship.company} logo`}
											fill
											className="object-cover"
										/>
									</div>

									<p className="font-regular text-[20px] leading-[26px] text-[#71717A]">
										{scholarship.company}
									</p>
								</div>
								<h2 className="font-regular text-[#18181B] text-[48px] sm:text-[48px] leading-5 sm:leading-[64px] max-w-[833px]">
									{scholarship.title}
								</h2>
							</div>

							{/* white cards section */}
							<div className="grid grid-cols-3 gap-y-6 p-5 mt-6 bg-white">
								{/* details cards */}
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<CalendarCheckIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Date Posted
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											Jan 30, 2025
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<DeadlineIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Application Deadline
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											June 30, 2025
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<AwardIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Award Amount
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											$25,000
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<PeopleIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Number of Recipients
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											1,200
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<GlobeIcon color="#52525B" />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Mode of Study
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											In-person
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<LocationIcon color="#52525B" />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Location
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											Pan-African
										</p>
									</div>
								</div>
							</div>

							<div>
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									About this Scholarship
								</p>
								<p className="text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
									The African Future Leaders Scholarship Program is a flagship
									initiative of the African Leadership Foundation, established
									to identify, develop, and connect the next generation of
									African leaders. This prestigious scholarship program is built
									on the belief that Africa&apos;s transformation will be driven by a
									new generation of ethical, committed, and visionary leaders
									across various sectors. The program provides comprehensive
									financial support covering tuition fees, accommodation, books,
									travel, and a stipend for living expenses. Beyond financial
									assistance, scholars benefit from leadership development
									workshops, mentorship from industry leaders, internship
									placements, and networking opportunities with a community of
									like-minded future leaders. Upon graduation, scholars join an
									exclusive alumni network that continues to provide
									professional development, collaboration opportunities, and
									lifelong connections across the continent.
								</p>
							</div>

							<div>
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									Eligibility Criteria
								</p>
								<ul className=" ">
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Must be a citizen of an African country
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Applying to or accepted into an undergraduate program at
										an accredited university
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Demonstrated academic excellence (minimum GPA of 3.5 or
										equivalent)
									</li>
									<li className="text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Proven leadership potential through extracurricular
										activities, community service, or entrepreneurial
										initiatives
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Financial need
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										* Age between 17-25 years at the time of application
									</li>
								</ul>
							</div>
							<div>
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									Application Process
								</p>
								<ol className=" ">
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										1. Complete the online application form
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										2. Submit required documents
									</li>
									<li className="text-[20px] leading-[12px] font-regular text-[#71717A] mt-[14px]">
										3. Complete a video interview if shortlisted
									</li>
									<li className="text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
										4. Participate in a leadership assessment day for finalists
									</li>
								</ol>
							</div>

							<div>
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									Required Documents
								</p>

								<div className="grid grid-cols-2 mt-[14px]">
									<div className="flex flex-col gap-4">
										<div className="flex items-center gap-2.5">
											<DoubleCheckIcon className="w-8 h-8" />
											<p className="text-[20px] leading-[12px] font-regular text-[#71717A]">
												Academic transcripts
											</p>
										</div>
										<div className="flex items-center gap-2.5">
											<DoubleCheckIcon className="w-8 h-8" />
											<p className="text-[20px] leading-[12px] font-regular text-[#71717A]">
												Two recommendation letters
											</p>
										</div>
										<div className="flex items-center gap-2.5">
											<DoubleCheckIcon className="w-8 h-8" />
											<p className="text-[20px] leading-[12px] font-regular text-[#71717A]">
												Leadership essay (800 words)
											</p>
										</div>
									</div>
									<div className="flex flex-col gap-4">
										<div className="flex items-center gap-2.5">
											<DoubleCheckIcon className="w-8 h-8" />
											<p className="text-[20px] leading-[12px] font-regular text-[#71717A]">
												Proof of identity (passport or national ID)
											</p>
										</div>
										<div className="flex items-center gap-2.5">
											<DoubleCheckIcon className="w-8 h-8" />
											<p className="text-[20px] leading-[12px] font-regular text-[#71717A]">
												Personal statement (500 words)
											</p>
										</div>
										<div className="flex items-center gap-2.5">
											<DoubleCheckIcon className="w-8 h-8" />
											<p className="text-[20px] leading-[12px] font-regular text-[#71717A]">
												Proof of financial need
											</p>
										</div>
									</div>
								</div>
							</div>

							<div>
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									Selection Process
								</p>

								<p className="text-[20px] leading-[26px] font-regular text-[#71717A] mt-[14px]">
									Applications are reviewed by a committee of education experts
									and foundation representatives. Shortlisted candidates
									complete a video interview, and finalists are invited to a
									leadership assessment day where they participate in group
									activities and individual interviews.
								</p>
							</div>

							<div className=" flex flex-col gap-3.5">
								<p className="font-semibold text-[24px] text-[#52525B] leading-[22px]">
									Scholarship Materials
								</p>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Application Guidelines.pdf
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												1.2 MB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Scholarship FAQ.pdf
											</p>
											<p className="text-sm leading-[22px]  font-medium text-[#71717A]">
												920 KB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>
						<aside>
							<SubmissionDetailsCard  />

							<Button
								variant="primary"
								className="w-full mt-4 focus:outline-none shadow-none font-semibold">
								Apply Now
							</Button>
							<Button
								variant="outline"
								leftIcon={<ShareIcon color="#335CFF" />}
								className="w-full mt-4 focus:outline-none shadow-none ">
								Share Scholarship
							</Button>
						</aside>
					</div>
				</div>
			</section>

			<Footer />
		</main>
	);
}
