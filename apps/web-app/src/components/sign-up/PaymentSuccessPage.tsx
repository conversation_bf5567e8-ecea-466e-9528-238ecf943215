import Button from "@ui/ui/Button";
import { AfricaSkillzLogoColoredIcon } from "../common/icons";
import Image from "next/image";
import Link from "next/link";

export default function PaymentSuccessPage() {
	return (
		<main className="w-full h-screen relative bg-white">
			<div className="p-[32px] h-screen z-40 ">
				<AfricaSkillzLogoColoredIcon className="w-[116px] h-[42px]" />

				<div className="mt-[64px] m-auto max-w-[783px] flex flex-col items-center z-40">
					<div className="py-5 mr-auto">
						<div className="">
							<h2 className="font-semibold text-[18px] leading-[24px] text-[#27272A]">
								Welcome to <span className="text-[#335CFF]">Professional Plan!</span>
							</h2>
							<p className="text-[#71717A] font-medium leading-[22px] ">
								Enjoy all the perks and benefit ahead.
							</p>
						</div>
					</div>

					<div className="p-8 flex gap-5 border border-[#E4E4E7] rounded-2xl w-[783px]">
						<div className="min-w-[294px] h-[314px] relative ">
							<Image src="/success-trophy.png" fill alt="success img" priority className="object-cover" />
						</div>

						<div className="w-full">
							<span className="px-3 py-1 rounded-full bg-[#EBEFFF] mr-2 w-full text-[#71717A] font-semibold text-[18px] leading-[24px] mb-2">
									Professional plan
								{/* <h3 className="text-[#71717A] font-semibold text-[18px] leading-[24px] ">
								</h3> */}
							</span>

							<p className="text-[#3F3F46] text-[16px] whitespace-nowrap leading-[26px] font-semibold mt-2 mb-2">
								Ideal for growing companies
							</p>

							<div className="mb-2 ">
								<div className="flex items-center gap-2 sm:gap-3 mb-3">
									<svg
										width="23"
										height="22"
										viewBox="0 0 23 22"
										fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<path
											d="M11.75 2.0625C9.92233 2.0625 8.25436 2.58668 6.7846 3.56874C5.31483 4.55081 4.16929 5.94665 3.49283 7.57977C2.81637 9.21288 2.63938 11.0099 2.98424 12.7436C3.32909 14.4773 4.18031 16.0698 5.43024 17.3198C6.68017 18.5697 8.27268 19.4209 10.0064 19.7658C11.7401 20.1106 13.5371 19.9336 15.1702 19.2572C16.8034 18.5807 18.1992 17.4352 19.1813 15.9654C20.1633 14.4956 20.6875 12.7677 20.6875 11C20.685 8.6304 19.7426 6.35856 18.067 4.683C16.3914 3.00743 14.1196 2.065 11.75 2.0625ZM15.6739 9.42391L10.8614 14.2364C10.7976 14.3003 10.7217 14.351 10.6383 14.3856C10.5548 14.4202 10.4654 14.438 10.375 14.438C10.2847 14.438 10.1952 14.4202 10.1117 14.3856C10.0283 14.351 9.95245 14.3003 9.8886 14.2364L7.8261 12.1739C7.69709 12.0449 7.62462 11.8699 7.62462 11.6875C7.62462 11.5051 7.69709 11.3301 7.8261 11.2011C7.9551 11.0721 8.13007 10.9996 8.3125 10.9996C8.49494 10.9996 8.66991 11.0721 8.79891 11.2011L10.375 12.778L14.7011 8.45109C14.765 8.38722 14.8408 8.33655 14.9243 8.30198C15.0077 8.26741 15.0972 8.24962 15.1875 8.24962C15.2778 8.24962 15.3673 8.26741 15.4507 8.30198C15.5342 8.33655 15.61 8.38722 15.6739 8.45109C15.7378 8.51497 15.7885 8.5908 15.823 8.67426C15.8576 8.75772 15.8754 8.84717 15.8754 8.9375C15.8754 9.02783 15.8576 9.11728 15.823 9.20074C15.7885 9.2842 15.7378 9.36003 15.6739 9.42391Z"
											fill="#7692FF"
										/>
									</svg>

									<span className="text-[#71717A] text-[16px] whitespace-nowrap leading-[26px] font-semibold">
										15 job postings per month
									</span>
								</div>
							</div>
							<div className="mb-2 ">
								<div className="flex items-center gap-2 sm:gap-3 mb-3">
									<svg
										width="23"
										height="22"
										viewBox="0 0 23 22"
										fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<path
											d="M11.75 2.0625C9.98233 2.0625 8.25436 2.58668 6.7846 3.56874C5.31483 4.55081 4.16929 5.94665 3.49283 7.57977C2.81637 9.21288 2.63938 11.0099 2.98424 12.7436C3.32909 14.4773 4.18031 16.0698 5.43024 17.3198C6.68017 18.5697 8.27268 19.4209 10.0064 19.7658C11.7401 20.1106 13.5371 19.9336 15.1702 19.2572C16.8034 18.5807 18.1992 17.4352 19.1813 15.9654C20.1633 14.4956 20.6875 12.7677 20.6875 11C20.685 8.6304 19.7426 6.35856 18.067 4.683C16.3914 3.00743 14.1196 2.065 11.75 2.0625ZM15.6739 9.42391L10.8614 14.2364C10.7976 14.3003 10.7217 14.351 10.6383 14.3856C10.5548 14.4202 10.4654 14.438 10.375 14.438C10.2847 14.438 10.1952 14.4202 10.1117 14.3856C10.0283 14.351 9.95245 14.3003 9.8886 14.2364L7.8261 12.1739C7.69709 12.0449 7.62462 11.8699 7.62462 11.6875C7.62462 11.5051 7.69709 11.3301 7.8261 11.2011C7.9551 11.0721 8.13007 10.9996 8.3125 10.9996C8.49494 10.9996 8.66991 11.0721 8.79891 11.2011L10.375 12.778L14.7011 8.45109C14.765 8.38722 14.8408 8.33655 14.9243 8.30198C15.0077 8.26741 15.0972 8.24962 15.1875 8.24962C15.2778 8.24962 15.3673 8.26741 15.4507 8.30198C15.5342 8.33655 15.61 8.38722 15.6739 8.45109C15.7378 8.51497 15.7885 8.5908 15.823 8.67426C15.8576 8.75772 15.8754 8.84717 15.8754 8.9375C15.8754 9.02783 15.8576 9.11728 15.823 9.20074C15.7885 9.2842 15.7378 9.36003 15.6739 9.42391Z"
											fill="#7692FF"
										/>
									</svg>

									<span className="text-[#71717A] text-[16px] whitespace-nowrap leading-[26px] font-semibold">
                                    Detailed analytics
									</span>
								</div>
							</div>
							<div className="mb-2 ">
								<div className="flex items-center gap-2 sm:gap-3 mb-3">
									<svg
										width="23"
										height="22"
										viewBox="0 0 23 22"
										fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<path
											d="M11.75 2.0625C9.98233 2.0625 8.25436 2.58668 6.7846 3.56874C5.31483 4.55081 4.16929 5.94665 3.49283 7.57977C2.81637 9.21288 2.63938 11.0099 2.98424 12.7436C3.32909 14.4773 4.18031 16.0698 5.43024 17.3198C6.68017 18.5697 8.27268 19.4209 10.0064 19.7658C11.7401 20.1106 13.5371 19.9336 15.1702 19.2572C16.8034 18.5807 18.1992 17.4352 19.1813 15.9654C20.1633 14.4956 20.6875 12.7677 20.6875 11C20.685 8.6304 19.7426 6.35856 18.067 4.683C16.3914 3.00743 14.1196 2.065 11.75 2.0625ZM15.6739 9.42391L10.8614 14.2364C10.7976 14.3003 10.7217 14.351 10.6383 14.3856C10.5548 14.4202 10.4654 14.438 10.375 14.438C10.2847 14.438 10.1952 14.4202 10.1117 14.3856C10.0283 14.351 9.95245 14.3003 9.8886 14.2364L7.8261 12.1739C7.69709 12.0449 7.62462 11.8699 7.62462 11.6875C7.62462 11.5051 7.69709 11.3301 7.8261 11.2011C7.9551 11.0721 8.13007 10.9996 8.3125 10.9996C8.49494 10.9996 8.66991 11.0721 8.79891 11.2011L10.375 12.778L14.7011 8.45109C14.765 8.38722 14.8408 8.33655 14.9243 8.30198C15.0077 8.26741 15.0972 8.24962 15.1875 8.24962C15.2778 8.24962 15.3673 8.26741 15.4507 8.30198C15.5342 8.33655 15.61 8.38722 15.6739 8.45109C15.7378 8.51497 15.7885 8.5908 15.823 8.67426C15.8576 8.75772 15.8754 8.84717 15.8754 8.9375C15.8754 9.02783 15.8576 9.11728 15.823 9.20074C15.7885 9.2842 15.7378 9.36003 15.6739 9.42391Z"
											fill="#7692FF"
										/>
									</svg>

									<span className="text-[#71717A] text-[16px] whitespace-nowrap leading-[26px] font-semibold">
                                    Advanced candidate search
									</span>
								</div>
							</div>
							<div className="mb-2 ">
								<div className="flex items-center gap-2 sm:gap-3 mb-3">
									<svg
										width="23"
										height="22"
										viewBox="0 0 23 22"
										fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<path
											d="M11.75 2.0625C9.98233 2.0625 8.25436 2.58668 6.7846 3.56874C5.31483 4.55081 4.16929 5.94665 3.49283 7.57977C2.81637 9.21288 2.63938 11.0099 2.98424 12.7436C3.32909 14.4773 4.18031 16.0698 5.43024 17.3198C6.68017 18.5697 8.27268 19.4209 10.0064 19.7658C11.7401 20.1106 13.5371 19.9336 15.1702 19.2572C16.8034 18.5807 18.1992 17.4352 19.1813 15.9654C20.1633 14.4956 20.6875 12.7677 20.6875 11C20.685 8.6304 19.7426 6.35856 18.067 4.683C16.3914 3.00743 14.1196 2.065 11.75 2.0625ZM15.6739 9.42391L10.8614 14.2364C10.7976 14.3003 10.7217 14.351 10.6383 14.3856C10.5548 14.4202 10.4654 14.438 10.375 14.438C10.2847 14.438 10.1952 14.4202 10.1117 14.3856C10.0283 14.351 9.95245 14.3003 9.8886 14.2364L7.8261 12.1739C7.69709 12.0449 7.62462 11.8699 7.62462 11.6875C7.62462 11.5051 7.69709 11.3301 7.8261 11.2011C7.9551 11.0721 8.13007 10.9996 8.3125 10.9996C8.49494 10.9996 8.66991 11.0721 8.79891 11.2011L10.375 12.778L14.7011 8.45109C14.765 8.38722 14.8408 8.33655 14.9243 8.30198C15.0077 8.26741 15.0972 8.24962 15.1875 8.24962C15.2778 8.24962 15.3673 8.26741 15.4507 8.30198C15.5342 8.33655 15.61 8.38722 15.6739 8.45109C15.7378 8.51497 15.7885 8.5908 15.823 8.67426C15.8576 8.75772 15.8754 8.84717 15.8754 8.9375C15.8754 9.02783 15.8576 9.11728 15.823 9.20074C15.7885 9.2842 15.7378 9.36003 15.6739 9.42391Z"
											fill="#7692FF"
										/>
									</svg>

									<span className="text-[#71717A] text-[16px] whitespace-nowrap leading-[26px] font-semibold">
									Featured job listings
									</span>
								</div>
							</div>
							<div className="mb-2 ">
								<div className="flex items-center gap-2 sm:gap-3 mb-3">
									<svg
										width="23"
										height="22"
										viewBox="0 0 23 22"
										fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<path
											d="M11.75 2.0625C9.98233 2.0625 8.25436 2.58668 6.7846 3.56874C5.31483 4.55081 4.16929 5.94665 3.49283 7.57977C2.81637 9.21288 2.63938 11.0099 2.98424 12.7436C3.32909 14.4773 4.18031 16.0698 5.43024 17.3198C6.68017 18.5697 8.27268 19.4209 10.0064 19.7658C11.7401 20.1106 13.5371 19.9336 15.1702 19.2572C16.8034 18.5807 18.1992 17.4352 19.1813 15.9654C20.1633 14.4956 20.6875 12.7677 20.6875 11C20.685 8.6304 19.7426 6.35856 18.067 4.683C16.3914 3.00743 14.1196 2.065 11.75 2.0625ZM15.6739 9.42391L10.8614 14.2364C10.7976 14.3003 10.7217 14.351 10.6383 14.3856C10.5548 14.4202 10.4654 14.438 10.375 14.438C10.2847 14.438 10.1952 14.4202 10.1117 14.3856C10.0283 14.351 9.95245 14.3003 9.8886 14.2364L7.8261 12.1739C7.69709 12.0449 7.62462 11.8699 7.62462 11.6875C7.62462 11.5051 7.69709 11.3301 7.8261 11.2011C7.9551 11.0721 8.13007 10.9996 8.3125 10.9996C8.49494 10.9996 8.66991 11.0721 8.79891 11.2011L10.375 12.778L14.7011 8.45109C14.765 8.38722 14.8408 8.33655 14.9243 8.30198C15.0077 8.26741 15.0972 8.24962 15.1875 8.24962C15.2778 8.24962 15.3673 8.26741 15.4507 8.30198C15.5342 8.33655 15.61 8.38722 15.6739 8.45109C15.7378 8.51497 15.7885 8.5908 15.823 8.67426C15.8576 8.75772 15.8754 8.84717 15.8754 8.9375C15.8754 9.02783 15.8576 9.11728 15.823 9.20074C15.7885 9.2842 15.7378 9.36003 15.6739 9.42391Z"
											fill="#7692FF"
										/>
									</svg>

									<span className="text-[#71717A] text-[16px] whitespace-nowrap leading-[26px] font-semibold">
                                    Priority email support
									</span>
								</div>
							</div>

                            <Link href={"/dashboard"}>
                            
                            <Button variant="primary" size="md" className="mt-5 w-full">Continue to Dashboard</Button>
                            </Link>
						</div>
					</div>
				</div>
			</div>
			<div className="w-full h-[300px] bg-gradient-to-t from-[#A1B4FF] via-transparent to-transparent  absolute bottom-0 "></div>
		</main>
	);
}
