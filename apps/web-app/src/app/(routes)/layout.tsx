// src/app/(app-shell)/layout.tsx
import {
	BellIcon,
	ChartIcon,
	CrownIcon,
	DollarIcon,
	HomeIcon,
	PlusIcon,
	ProfileIcon1,
} from "@/components/common/icons";
import { AfricaSkillzLogoColoredIcon, CaretIcon, SuitcaseIcon, SuitcaseIcon1 } from "@ui/icons";
// import { HomeIcon } from "lucide-react";
import React from "react";
import Image from "next/image";
import Button from "@ui/ui/Button";

export default function RoutesLayout({ children }: { children: React.ReactNode }) {
	return (
		<div className="grid grid-cols-[18%_82%] h-screen">
			<aside className=" bg-[#FFFFFF] h-screen border-r border-[#E4E4E7] min-w-[272px]">
				<div className="p-3">
					<AfricaSkillzLogoColoredIcon className="w-[116px] h-[42px]" />
				</div>
				<div className=" flex flex-col gap-5 p-5 ">
					<div>
						<div className="p-1 mb-2">
							<p className="font-medium text-sm text-[#71717A]">MAIN</p>
						</div>

						<div className="flex flex-col gap-1">
							<button className="pl-3 py-2 pr-2 flex items-center cursor-pointer  gap-2 bg-[#F4F4F5] rounded-[8px]">
								<HomeIcon className="text-[#335CFF] w-5 h-5" />{" "}
								<p className=" font-medium text-sm text-[#18181B] w-fit mr-auto ">Home</p>{" "}
								<CaretIcon color="#71717A" className="ml-auto" />
							</button>
							<button className="pl-3 py-2 pr-2 flex items-center cursor-pointer  gap-2  rounded-[8px]">
								<SuitcaseIcon className="text-[#335CFF] w-5 h-5" color="#71717A" />{" "}
								<p className=" font-medium text-sm text-[#71717A] w-fit mr-auto ">Jobs</p>{" "}
								{/* <CaretIcon color="#71717A" className="ml-auto" /> */}
							</button>
							<button className="pl-3 py-2 pr-2 flex items-center cursor-pointer  gap-2 rounded-[8px]">
								<ChartIcon color="#71717A" className="text-[#335CFF] w-5 h-5" />{" "}
								<p className=" font-medium text-sm text-[#71717A] w-fit mr-auto ">
									Reports & Analysis
								</p>{" "}
								{/* <CaretIcon color="#71717A" className="ml-auto" /> */}
							</button>
						</div>
					</div>

					<div>
						<div className="p-1 mb-2">
							<p className="font-medium text-sm text-[#71717A]">UTILITY</p>
						</div>

						<div className="flex flex-col gap-1">
							{/* <button className="pl-3 py-2 pr-2 flex items-center cursor-pointer  gap-2 bg-[#F4F4F5] rounded-[8px]">
								<HomeIcon className="text-[#335CFF] w-5 h-5" />{" "}
								<p className=" font-medium text-sm text-[#18181B] w-fit mr-auto ">Home</p>{" "}
								<CaretIcon color="#71717A" className="ml-auto" />
							</button> */}
							<button className="pl-3 py-2 pr-2 flex items-center cursor-pointer  gap-2  rounded-[8px]">
								<ProfileIcon1 className="text-[#335CFF] w-5 h-5" color="#71717A" />{" "}
								<p className=" font-medium text-sm text-[#71717A] w-fit mr-auto ">Profile</p>{" "}
								{/* <CaretIcon color="#71717A" className="ml-auto" /> */}
							</button>
							<button className="pl-3 py-2 pr-2 flex items-center cursor-pointer  gap-2 rounded-[8px]">
								<DollarIcon color="#71717A" className="text-[#335CFF] w-5 h-5" />{" "}
								<p className=" font-medium text-sm text-[#71717A] w-fit mr-auto ">Billing</p>{" "}
								{/* <CaretIcon color="#71717A" className="ml-auto" /> */}
							</button>
						</div>
					</div>
				</div>

				<div className="p-5 align-bottom mt-50">
					<div className="bg-[#EBEFFF] rounded-[8px] p-4 w-full flex items-center justify-between ">
						<p className="font-regular text-[12px] leading-[16px] text-[#335CFF]">Starter Plan</p>
						<CrownIcon />
					</div>

					<div className="flex items-center gap-2 mt-5">
						<div className="w-10 h-10">
							<Image
								src="/recruiter-signup.png"
								width={40}
								height={40}
								alt="profile image"
								className="rounded-full object-cover"
							/>
						</div>

						<div>
							<p className=" font-medium text-xs text-[#18181B] w-fit mb-1  ">Jesugbami Alagbara</p>
							<p className=" font-normal text-xs text-[#71717A] w-fit  ">
								<EMAIL>
							</p>
						</div>
					</div>
				</div>
			</aside>
			<div className="flex-1 flex flex-col w-full h-screen">
				<nav className="px-8 py-5 border-b border-[#E4E4E7] bg-white flex items-center justify-between">
					{/* Navbar content here */}

					<div className="flex items-center gap-2">
						<div className=" p-3 border border-[#E4E4E7] w-fit rounded-full">
							<HomeIcon color="#27272A" className="w-6 h-6" />
						</div>

						<div>
							<p className=" font-medium text-sm text-[#18181B] w-fit mb-1  ">Home</p>
							<p className=" font-normal text-xs text-[#71717A] w-fit  ">
								Recruiters dashboard overview{" "}
							</p>
						</div>
					</div>

					<div className="flex items-center gap-4">
						<Button variant="primary" size="xs" rightIcon={<PlusIcon />}>
							Post a Job
						</Button>
						<button className=" p-2.5  bg-[#E4E4E7] w-fit rounded-full">
							<BellIcon color="#27272A" className="w-5 h-5" />
						</button>
					</div>
				</nav>
				<main className="flex-1 bg-white h-full overflow-y-auto">{children}</main>
			</div>
		</div>
	);
}
